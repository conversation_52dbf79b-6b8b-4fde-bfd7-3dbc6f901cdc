#!/usr/bin/env node

/**
 * JWT密钥生成脚本
 * 用于生成安全的JWT密钥
 * 
 * 使用方法:
 * node scripts/generate-jwt-secret.js
 * 
 * 或者在package.json中添加脚本:
 * "generate-jwt-secret": "node scripts/generate-jwt-secret.js"
 */

const crypto = require('crypto');

/**
 * 生成安全的JWT密钥
 * @param {number} length 密钥长度（字节）
 * @returns {string} Base64编码的密钥
 */
function generateJWTSecret(length = 32) {
    return crypto.randomBytes(length).toString('base64');
}

/**
 * 生成十六进制密钥
 * @param {number} length 密钥长度（字节）
 * @returns {string} 十六进制密钥
 */
function generateHexSecret(length = 32) {
    return crypto.randomBytes(length).toString('hex');
}

/**
 * 验证密钥强度
 * @param {string} secret 密钥
 * @returns {object} 验证结果
 */
function validateSecretStrength(secret) {
    const result = {
        isValid: true,
        warnings: [],
        recommendations: []
    };

    // 检查长度
    if (secret.length < 32) {
        result.isValid = false;
        result.warnings.push('密钥长度不足32字符，安全性较低');
        result.recommendations.push('建议使用至少32字符的密钥');
    }

    // 检查是否包含常见弱密钥模式
    const weakPatterns = [
        'secret',
        'password',
        '123456',
        'qwerty',
        'admin',
        'test',
        'default'
    ];

    const lowerSecret = secret.toLowerCase();
    weakPatterns.forEach(pattern => {
        if (lowerSecret.includes(pattern)) {
            result.isValid = false;
            result.warnings.push(`密钥包含常见弱模式: ${pattern}`);
            result.recommendations.push('避免使用常见单词或模式');
        }
    });

    // 检查字符多样性
    const hasUpperCase = /[A-Z]/.test(secret);
    const hasLowerCase = /[a-z]/.test(secret);
    const hasNumbers = /[0-9]/.test(secret);
    const hasSpecialChars = /[^A-Za-z0-9]/.test(secret);

    const diversity = [hasUpperCase, hasLowerCase, hasNumbers, hasSpecialChars].filter(Boolean).length;
    
    if (diversity < 3) {
        result.warnings.push('密钥字符多样性不足');
        result.recommendations.push('建议包含大小写字母、数字和特殊字符');
    }

    return result;
}

function main() {
    console.log('🔐 WTMS JWT密钥生成工具');
    console.log('================================\n');

    // 生成不同格式的密钥
    const base64Secret = generateJWTSecret(32);
    const hexSecret = generateHexSecret(32);
    const longBase64Secret = generateJWTSecret(64);

    console.log('📋 生成的JWT密钥选项：\n');

    console.log('1. 标准Base64密钥 (32字节):');
    console.log(`   JWT_SECRET=${base64Secret}\n`);

    console.log('2. 十六进制密钥 (32字节):');
    console.log(`   JWT_SECRET=${hexSecret}\n`);

    console.log('3. 长Base64密钥 (64字节，推荐用于生产环境):');
    console.log(`   JWT_SECRET=${longBase64Secret}\n`);

    console.log('🔍 密钥强度验证：');
    console.log('================================\n');

    // 验证生成的密钥
    const validation = validateSecretStrength(base64Secret);
    console.log(`标准Base64密钥强度: ${validation.isValid ? '✅ 强' : '❌ 弱'}`);
    if (validation.warnings.length > 0) {
        validation.warnings.forEach(warning => console.log(`⚠️  ${warning}`));
    }
    if (validation.recommendations.length > 0) {
        validation.recommendations.forEach(rec => console.log(`💡 ${rec}`));
    }

    console.log('\n📝 使用说明：');
    console.log('================================');
    console.log('1. 复制上述任一密钥');
    console.log('2. 在.env文件中设置: JWT_SECRET=你的密钥');
    console.log('3. 确保.env文件已添加到.gitignore中');
    console.log('4. 生产环境使用不同的密钥');
    console.log('5. 定期轮换密钥（建议90天）');

    console.log('\n⚠️  安全提醒：');
    console.log('================================');
    console.log('• 绝不要在代码中硬编码密钥');
    console.log('• 不要在版本控制中提交密钥');
    console.log('• 生产环境和开发环境使用不同密钥');
    console.log('• 定期更换密钥');
    console.log('• 使用环境变量或密钥管理服务');

    console.log('\n🛠️  生成命令参考：');
    console.log('================================');
    console.log('Linux/macOS: openssl rand -base64 32');
    console.log('Windows PowerShell: [System.Convert]::ToBase64String((1..32 | ForEach {Get-Random -Maximum 256}))');
    console.log('Node.js: require("crypto").randomBytes(32).toString("base64")');
}

// 如果直接运行此脚本
if (require.main === module) {
    main();
}

module.exports = {
    generateJWTSecret,
    generateHexSecret,
    validateSecretStrength
};
