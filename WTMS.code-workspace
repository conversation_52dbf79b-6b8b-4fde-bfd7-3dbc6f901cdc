{"folders": [{"name": "WTMS Root", "path": "."}, {"name": "Frontend", "path": "./wtms-frontend"}, {"name": "Backend", "path": "./wtms-backend"}, {"name": "<PERSON><PERSON><PERSON>", "path": "./scripts"}], "settings": {"java.compile.nullAnalysis.mode": "automatic", "java.configuration.updateBuildConfiguration": "automatic", "editor.tabSize": 2, "editor.insertSpaces": true, "editor.formatOnSave": true, "files.encoding": "utf8", "files.eol": "\n", "files.trimTrailingWhitespace": true, "files.insertFinalNewline": true, "files.exclude": {"**/node_modules": true, "**/target": true, "**/.git": true, "**/.DS_Store": true, "**/Thumbs.db": true, "**/*.class": true, "**/.idea": true}, "search.exclude": {"**/node_modules": true, "**/target": true, "**/dist": true, "**/build": true, "**/*.log": true}, "files.associations": {"*.vue": "vue", "*.yml": "yaml", "*.yaml": "yaml", ".env*": "properties", "Dockerfile*": "dockerfile"}, "[vue]": {"editor.defaultFormatter": "esbenp.prettier-vscode", "editor.tabSize": 2}, "[typescript]": {"editor.defaultFormatter": "esbenp.prettier-vscode", "editor.tabSize": 2}, "[javascript]": {"editor.defaultFormatter": "esbenp.prettier-vscode", "editor.tabSize": 2}, "[java]": {"editor.defaultFormatter": "redhat.java", "editor.tabSize": 4}, "[yaml]": {"editor.defaultFormatter": "redhat.vscode-yaml", "editor.tabSize": 2}, "eslint.validate": ["javascript", "javascriptreact", "typescript", "typescriptreact", "vue"], "eslint.workingDirectories": ["./wtms-frontend"], "volar.takeOverMode.enabled": true, "typescript.preferences.importModuleSpecifier": "relative", "git.autofetch": true, "terminal.integrated.defaultProfile.windows": "PowerShell"}, "extensions": {"recommendations": ["vscjava.vscode-java-pack", "vmware.vscode-spring-boot", "vue.volar", "vue.vscode-typescript-vue-plugin", "bradlc.vscode-tailwindcss", "formulahendry.auto-rename-tag", "christian-kohler.path-intellisense", "ms-vscode.vscode-typescript-next", "dbaeumer.vscode-eslint", "esbenp.prettier-vscode", "ms-mssql.mssql", "cweijan.vscode-mysql-client2", "redis.redis-for-vscode", "ms-azuretools.vscode-docker", "eamodio.gitlens", "mhutchie.git-graph", "sonarsource.sonarlint-vscode", "ms-vscode.vscode-json", "redhat.vscode-yaml", "yzhang.markdown-all-in-one", "ms-vscode.powershell", "gruntfuggly.todo-tree", "alefragnani.bookmarks", "streetsidesoftware.code-spell-checker", "humao.rest-client", "pkief.material-icon-theme"]}, "launch": {"version": "0.2.0", "configurations": [{"type": "java", "name": "启动 WTMS 后端应用", "request": "launch", "mainClass": "com.wtms.WtmsQuickStartApp", "projectName": "wtms-backend", "args": "--spring.profiles.active=dev", "vmArgs": "-Dspring.profiles.active=dev -Dfile.encoding=UTF-8"}]}, "tasks": {"version": "2.0.0", "tasks": [{"label": "WTMS: 启动前端开发服务器", "type": "shell", "command": "npm", "args": ["run", "dev"], "options": {"cwd": "${workspaceFolder}/wtms-frontend"}, "group": "build"}, {"label": "WTMS: 启动后端Spring Boot应用", "type": "shell", "command": "mvn", "args": ["spring-boot:run"], "options": {"cwd": "${workspaceFolder}/wtms-backend"}, "group": "build"}]}}