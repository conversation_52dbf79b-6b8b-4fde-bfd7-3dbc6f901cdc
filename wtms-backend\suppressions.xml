<?xml version="1.0"?>
<!DOCTYPE suppressions PUBLIC
    "-//Checkstyle//DTD SuppressionFilter Configuration 1.2//EN"
    "https://checkstyle.org/dtds/suppressions_1_2.dtd">

<suppressions>
    <!-- 抑制生成的代码检查 -->
    <suppress files="[\\/]generated[\\/]" checks=".*"/>
    <suppress files="[\\/]target[\\/]" checks=".*"/>
    
    <!-- 抑制测试代码的某些检查 -->
    <suppress files=".*Test\.java" checks="MagicNumber"/>
    <suppress files=".*Test\.java" checks="MethodLength"/>
    <suppress files=".*Test\.java" checks="JavadocMethod"/>
    <suppress files=".*Test\.java" checks="JavadocType"/>
    
    <!-- 抑制配置类的某些检查 -->
    <suppress files=".*Config\.java" checks="HideUtilityClassConstructor"/>
    <suppress files=".*Configuration\.java" checks="HideUtilityClassConstructor"/>
    
    <!-- 抑制实体类的某些检查 -->
    <suppress files=".*Entity\.java" checks="VisibilityModifier"/>
    <suppress files=".*DTO\.java" checks="VisibilityModifier"/>
    <suppress files=".*VO\.java" checks="VisibilityModifier"/>
    <suppress files=".*DO\.java" checks="VisibilityModifier"/>
    
    <!-- 抑制常量类的某些检查 -->
    <suppress files=".*Constants\.java" checks="HideUtilityClassConstructor"/>
    <suppress files=".*Constant\.java" checks="HideUtilityClassConstructor"/>
    
    <!-- 抑制工具类的某些检查 -->
    <suppress files=".*Utils\.java" checks="HideUtilityClassConstructor"/>
    <suppress files=".*Util\.java" checks="HideUtilityClassConstructor"/>
    
    <!-- 抑制Spring Boot主类的某些检查 -->
    <suppress files=".*Application\.java" checks="HideUtilityClassConstructor"/>
    <suppress files=".*App\.java" checks="HideUtilityClassConstructor"/>
    
    <!-- 抑制Mapper接口的某些检查 -->
    <suppress files=".*Mapper\.java" checks="InterfaceIsType"/>
    
    <!-- 抑制特定文件的检查 -->
    <suppress files="WtmsQuickStartApp\.java" checks="HideUtilityClassConstructor"/>
    
    <!-- 抑制包含@Data注解的类的某些检查 -->
    <suppress files=".*\.java" checks="EqualsHashCode" message=".*@Data.*"/>
    
    <!-- 抑制长度检查对于某些特殊情况 -->
    <suppress files=".*\.java" checks="LineLength" message=".*import.*"/>
    <suppress files=".*\.java" checks="LineLength" message=".*package.*"/>
</suppressions>
