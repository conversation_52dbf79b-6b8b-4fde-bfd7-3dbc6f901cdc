{"WTMS Controller": {"prefix": "wtms-controller", "body": ["@RestController", "@RequestMapping(\"/api/v1/${1:resource}\")", "@Slf4j", "@Api(tags = \"${2:Resource} API\")", "public class ${3:Resource}Controller {", "", "    @Autowired", "    private ${3:Resource}Service ${4:resourceService};", "", "    @GetMapping", "    @ApiOperation(\"获取${2:Resource}列表\")", "    public Result<List<${3:Resource}>> list() {", "        List<${3:Resource}> list = ${4:resourceService}.list();", "        return Result.success(list);", "    }", "", "    @GetMapping(\"/{id}\")", "    @ApiOperation(\"根据ID获取${2:Resource}\")", "    public Result<${3:Resource}> getById(@PathVariable Long id) {", "        ${3:Resource} entity = ${4:resourceService}.getById(id);", "        return Result.success(entity);", "    }", "", "    @PostMapping", "    @ApiOperation(\"创建${2:Resource}\")", "    public Result<${3:Resource}> create(@RequestBody @Valid ${3:Resource} entity) {", "        ${4:resourceService}.save(entity);", "        return Result.success(entity);", "    }", "", "    @PutMapping(\"/{id}\")", "    @ApiOperation(\"更新${2:Resource}\")", "    public Result<${3:Resource}> update(@PathVariable Long id, @RequestBody @Valid ${3:Resource} entity) {", "        entity.setId(id);", "        ${4:resourceService}.updateById(entity);", "        return Result.success(entity);", "    }", "", "    @DeleteMapping(\"/{id}\")", "    @ApiOperation(\"删除${2:Resource}\")", "    public Result<Void> delete(@PathVariable Long id) {", "        ${4:resourceService}.removeById(id);", "        return Result.success();", "    }", "}"], "description": "创建WTMS标准Controller"}, "WTMS Service": {"prefix": "wtms-service", "body": ["@Service", "@Slf4j", "public class ${1:Resource}Service extends ServiceImpl<${1:Resource}Mapper, ${1:Resource}> {", "", "    @Override", "    public boolean save(${1:Resource} entity) {", "        // 业务逻辑处理", "        ${2:// TODO: 添加业务逻辑}", "        return super.save(entity);", "    }", "", "    @Override", "    public boolean updateById(${1:Resource} entity) {", "        // 业务逻辑处理", "        ${3:// TODO: 添加业务逻辑}", "        return super.updateById(entity);", "    }", "", "    @Override", "    public boolean removeById(Serializable id) {", "        // 业务逻辑处理", "        ${4:// TODO: 添加业务逻辑}", "        return super.removeById(id);", "    }", "}"], "description": "创建WTMS标准Service"}, "WTMS Entity": {"prefix": "wtms-entity", "body": ["@Data", "@EqualsAndHashCode(callSuper = true)", "@TableName(\"${1:table_name}\")", "@ApiModel(description = \"${2:Entity Description}\")", "public class ${3:EntityName} extends BaseEntity {", "", "    @ApiModelProperty(\"${4:Field Description}\")", "    @TableField(\"${5:field_name}\")", "    private ${6:String} ${7:fieldName};", "", "    $0", "}"], "description": "创建WTMS标准Entity"}, "WTMS Vue Component": {"prefix": "wtms-vue-component", "body": ["<template>", "  <div class=\"${1:component-name}\">", "    <el-card>", "      <template #header>", "        <div class=\"card-header\">", "          <span>${2:Component Title}</span>", "        </div>", "      </template>", "      $0", "    </el-card>", "  </div>", "</template>", "", "<script setup lang=\"ts\">", "import { ref, reactive, onMounted } from 'vue'", "import { ElMessage } from 'element-plus'", "", "// 响应式数据", "const loading = ref(false)", "const data = reactive({", "  // TODO: 定义数据结构", "})", "", "// 生命周期", "onMounted(() => {", "  init()", "})", "", "// 初始化", "const init = async () => {", "  try {", "    loading.value = true", "    // TODO: 初始化逻辑", "  } catch (error) {", "    ElMessage.error('初始化失败')", "  } finally {", "    loading.value = false", "  }", "}", "</script>", "", "<style scoped lang=\"scss\">", ".${1:component-name} {", "  .card-header {", "    display: flex;", "    justify-content: space-between;", "    align-items: center;", "  }", "}", "</style>"], "description": "创建WTMS标准Vue组件"}, "WTMS API Request": {"prefix": "wtms-api", "body": ["import request from '@/utils/request'", "", "export interface ${1:Resource} {", "  id?: number", "  ${2:// TODO: 定义接口字段}", "}", "", "export interface ${1:Resource}Query {", "  ${3:// TODO: 定义查询参数}", "}", "", "// 获取${1:Resource}列表", "export const get${1:Resource}List = (params?: ${1:Resource}Query) => {", "  return request.get<${1:Resource}[]>('/api/v1/${4:resources}', { params })", "}", "", "// 根据ID获取${1:Resource}", "export const get${1:Resource}ById = (id: number) => {", "  return request.get<${1:Resource}>(`/api/v1/${4:resources}/${id}`)", "}", "", "// 创建${1:Resource}", "export const create${1:Resource} = (data: ${1:Resource}) => {", "  return request.post<${1:Resource}>('/api/v1/${4:resources}', data)", "}", "", "// 更新${1:Resource}", "export const update${1:Resource} = (id: number, data: ${1:Resource}) => {", "  return request.put<${1:Resource}>(`/api/v1/${4:resources}/${id}`, data)", "}", "", "// 删除${1:Resource}", "export const delete${1:Resource} = (id: number) => {", "  return request.delete(`/api/v1/${4:resources}/${id}`)", "}"], "description": "创建WTMS标准API请求"}}