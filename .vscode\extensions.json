{
  "recommendations": [
    // Java开发扩展
    "vscjava.vscode-java-pack",
    "vmware.vscode-spring-boot",
    "vscjava.vscode-spring-initializr",
    "vscjava.vscode-spring-boot-dashboard",
    
    // Vue.js和前端开发扩展
    "vue.volar",
    "vue.vscode-typescript-vue-plugin",
    "bradlc.vscode-tailwindcss",
    "formulahendry.auto-rename-tag",
    "christian-kohler.path-intellisense",
    
    // TypeScript和JavaScript扩展
    "ms-vscode.vscode-typescript-next",
    "dbaeumer.vscode-eslint",
    "esbenp.prettier-vscode",
    
    // 数据库扩展
    "ms-mssql.mssql",
    "cweijan.vscode-mysql-client2",
    "redis.redis-for-vscode",
    
    // Docker和容器扩展
    "ms-azuretools.vscode-docker",
    "ms-vscode-remote.remote-containers",
    
    // Git和版本控制扩展
    "eamodio.gitlens",
    "mhutchie.git-graph",
    "donjayamanne.githistory",
    
    // 代码质量和安全扩展
    "sonarsource.sonarlint-vscode",
    "ms-vscode.vscode-json",
    "redhat.vscode-yaml",
    
    // 文档和Markdown扩展
    "yzhang.markdown-all-in-one",
    "shd101wyy.markdown-preview-enhanced",
    "davidanson.vscode-markdownlint",
    
    // 实用工具扩展
    "ms-vscode.powershell",
    "ms-vscode-remote.remote-ssh",
    "gruntfuggly.todo-tree",
    "alefragnani.bookmarks",
    "streetsidesoftware.code-spell-checker",
    
    // API开发和测试扩展
    "humao.rest-client",
    "rangav.vscode-thunder-client",
    
    // 主题和界面扩展
    "pkief.material-icon-theme",
    "zhuangtongfa.material-theme",
    "ms-vscode.theme-tomorrowkit"
  ],
  "unwantedRecommendations": [
    // 避免推荐可能冲突的扩展
    "ms-vscode.vscode-typescript",
    "hookyqr.beautify"
  ]
}
