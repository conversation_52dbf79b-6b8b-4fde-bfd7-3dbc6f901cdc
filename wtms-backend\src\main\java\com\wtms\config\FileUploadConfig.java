package com.wtms.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.web.servlet.MultipartConfigFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.unit.DataSize;
import org.springframework.web.multipart.MultipartResolver;
import org.springframework.web.multipart.commons.CommonsMultipartResolver;

import javax.servlet.MultipartConfigElement;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

/**
 * 文件上传安全配置
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Configuration
@ConfigurationProperties(prefix = "wtms.file-upload")
public class FileUploadConfig {

    /**
     * 最大文件大小 (默认10MB)
     */
    private DataSize maxFileSize = DataSize.ofMegabytes(10);

    /**
     * 最大请求大小 (默认50MB)
     */
    private DataSize maxRequestSize = DataSize.ofMegabytes(50);

    /**
     * 文件大小阈值 (默认1MB)
     */
    private DataSize fileSizeThreshold = DataSize.ofMegabytes(1);

    /**
     * 上传临时目录
     */
    private String tempDir = System.getProperty("java.io.tmpdir");

    /**
     * 允许的文件类型
     */
    private Set<String> allowedFileTypes = new HashSet<>(Arrays.asList(
        // 图片文件
        "jpg", "jpeg", "png", "gif", "bmp", "webp", "svg",
        // 文档文件
        "pdf", "doc", "docx", "xls", "xlsx", "ppt", "pptx", "txt", "rtf",
        // 压缩文件
        "zip", "rar", "7z", "tar", "gz",
        // 其他常用文件
        "csv", "json", "xml"
    ));

    /**
     * 禁止的文件类型
     */
    private Set<String> forbiddenFileTypes = new HashSet<>(Arrays.asList(
        // 可执行文件
        "exe", "bat", "cmd", "com", "pif", "scr", "vbs", "js", "jar",
        // 脚本文件
        "sh", "bash", "ps1", "php", "asp", "aspx", "jsp",
        // 系统文件
        "dll", "sys", "ini", "reg",
        // 其他危险文件
        "msi", "deb", "rpm", "dmg", "iso"
    ));

    /**
     * 允许的MIME类型
     */
    private Set<String> allowedMimeTypes = new HashSet<>(Arrays.asList(
        // 图片MIME类型
        "image/jpeg", "image/png", "image/gif", "image/bmp", "image/webp", "image/svg+xml",
        // 文档MIME类型
        "application/pdf",
        "application/msword",
        "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
        "application/vnd.ms-excel",
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        "application/vnd.ms-powerpoint",
        "application/vnd.openxmlformats-officedocument.presentationml.presentation",
        "text/plain", "text/rtf",
        // 压缩文件MIME类型
        "application/zip", "application/x-rar-compressed", "application/x-7z-compressed",
        "application/x-tar", "application/gzip",
        // 其他MIME类型
        "text/csv", "application/json", "application/xml", "text/xml"
    ));

    /**
     * 上传目录
     */
    private String uploadDir = "uploads";

    /**
     * 是否启用文件类型检查
     */
    private boolean enableFileTypeCheck = true;

    /**
     * 是否启用MIME类型检查
     */
    private boolean enableMimeTypeCheck = true;

    /**
     * 是否启用文件内容检查
     */
    private boolean enableContentCheck = true;

    /**
     * 最大文件名长度
     */
    private int maxFilenameLength = 255;

    @Bean
    public MultipartConfigElement multipartConfigElement() {
        MultipartConfigFactory factory = new MultipartConfigFactory();
        factory.setMaxFileSize(maxFileSize);
        factory.setMaxRequestSize(maxRequestSize);
        factory.setFileSizeThreshold(fileSizeThreshold);
        factory.setLocation(tempDir);
        return factory.createMultipartConfig();
    }

    @Bean
    public MultipartResolver multipartResolver() {
        CommonsMultipartResolver resolver = new CommonsMultipartResolver();
        resolver.setMaxUploadSize(maxRequestSize.toBytes());
        resolver.setMaxUploadSizePerFile(maxFileSize.toBytes());
        resolver.setMaxInMemorySize((int) fileSizeThreshold.toBytes());
        resolver.setDefaultEncoding("UTF-8");
        return resolver;
    }

    /**
     * 检查文件扩展名是否被允许
     */
    public boolean isFileTypeAllowed(String filename) {
        if (!enableFileTypeCheck) {
            return true;
        }
        
        String extension = getFileExtension(filename);
        if (extension == null) {
            return false;
        }
        
        extension = extension.toLowerCase();
        
        // 首先检查是否在禁止列表中
        if (forbiddenFileTypes.contains(extension)) {
            return false;
        }
        
        // 然后检查是否在允许列表中
        return allowedFileTypes.contains(extension);
    }

    /**
     * 检查MIME类型是否被允许
     */
    public boolean isMimeTypeAllowed(String mimeType) {
        if (!enableMimeTypeCheck) {
            return true;
        }
        
        return mimeType != null && allowedMimeTypes.contains(mimeType.toLowerCase());
    }

    /**
     * 检查文件名是否有效
     */
    public boolean isFilenameValid(String filename) {
        if (filename == null || filename.trim().isEmpty()) {
            return false;
        }
        
        // 检查文件名长度
        if (filename.length() > maxFilenameLength) {
            return false;
        }
        
        // 检查文件名中的危险字符
        String[] dangerousPatterns = {
            "..", "/", "\\", ":", "*", "?", "\"", "<", ">", "|",
            "CON", "PRN", "AUX", "NUL", "COM1", "COM2", "COM3", "COM4", "COM5",
            "COM6", "COM7", "COM8", "COM9", "LPT1", "LPT2", "LPT3", "LPT4",
            "LPT5", "LPT6", "LPT7", "LPT8", "LPT9"
        };
        
        String upperFilename = filename.toUpperCase();
        for (String pattern : dangerousPatterns) {
            if (upperFilename.contains(pattern)) {
                return false;
            }
        }
        
        return true;
    }

    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String filename) {
        if (filename == null || filename.isEmpty()) {
            return null;
        }
        
        int lastDotIndex = filename.lastIndexOf('.');
        if (lastDotIndex == -1 || lastDotIndex == filename.length() - 1) {
            return null;
        }
        
        return filename.substring(lastDotIndex + 1);
    }

    // Getters and Setters
    public DataSize getMaxFileSize() {
        return maxFileSize;
    }

    public void setMaxFileSize(DataSize maxFileSize) {
        this.maxFileSize = maxFileSize;
    }

    public DataSize getMaxRequestSize() {
        return maxRequestSize;
    }

    public void setMaxRequestSize(DataSize maxRequestSize) {
        this.maxRequestSize = maxRequestSize;
    }

    public DataSize getFileSizeThreshold() {
        return fileSizeThreshold;
    }

    public void setFileSizeThreshold(DataSize fileSizeThreshold) {
        this.fileSizeThreshold = fileSizeThreshold;
    }

    public String getTempDir() {
        return tempDir;
    }

    public void setTempDir(String tempDir) {
        this.tempDir = tempDir;
    }

    public Set<String> getAllowedFileTypes() {
        return allowedFileTypes;
    }

    public void setAllowedFileTypes(Set<String> allowedFileTypes) {
        this.allowedFileTypes = allowedFileTypes;
    }

    public Set<String> getForbiddenFileTypes() {
        return forbiddenFileTypes;
    }

    public void setForbiddenFileTypes(Set<String> forbiddenFileTypes) {
        this.forbiddenFileTypes = forbiddenFileTypes;
    }

    public Set<String> getAllowedMimeTypes() {
        return allowedMimeTypes;
    }

    public void setAllowedMimeTypes(Set<String> allowedMimeTypes) {
        this.allowedMimeTypes = allowedMimeTypes;
    }

    public String getUploadDir() {
        return uploadDir;
    }

    public void setUploadDir(String uploadDir) {
        this.uploadDir = uploadDir;
    }

    public boolean isEnableFileTypeCheck() {
        return enableFileTypeCheck;
    }

    public void setEnableFileTypeCheck(boolean enableFileTypeCheck) {
        this.enableFileTypeCheck = enableFileTypeCheck;
    }

    public boolean isEnableMimeTypeCheck() {
        return enableMimeTypeCheck;
    }

    public void setEnableMimeTypeCheck(boolean enableMimeTypeCheck) {
        this.enableMimeTypeCheck = enableMimeTypeCheck;
    }

    public boolean isEnableContentCheck() {
        return enableContentCheck;
    }

    public void setEnableContentCheck(boolean enableContentCheck) {
        this.enableContentCheck = enableContentCheck;
    }

    public int getMaxFilenameLength() {
        return maxFilenameLength;
    }

    public void setMaxFilenameLength(int maxFilenameLength) {
        this.maxFilenameLength = maxFilenameLength;
    }
}
