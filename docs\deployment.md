# WTMS 系统部署指南

## 1. 环境要求

### 1.1 硬件要求

#### 最小配置
- **CPU**: 2核心
- **内存**: 4GB RAM
- **存储**: 50GB SSD
- **网络**: 100Mbps

#### 推荐配置
- **CPU**: 4核心以上
- **内存**: 8GB RAM以上
- **存储**: 100GB SSD以上
- **网络**: 1Gbps

### 1.2 软件要求

#### 基础环境
- **操作系统**: Ubuntu 20.04+ / CentOS 8+ / Windows Server 2019+
- **Java**: JDK 8 或 JDK 11
- **Node.js**: 18.x LTS
- **MySQL**: 8.0+
- **Redis**: 7.0+
- **Nginx**: 1.20+ (可选)

#### 开发工具
- **Maven**: 3.6+
- **Git**: 2.30+
- **Docker**: 20.10+ (可选)
- **Docker Compose**: 2.0+ (可选)

## 2. 快速部署

### 2.1 使用Docker Compose部署（推荐）

#### 2.1.1 克隆项目
```bash
git clone https://github.com/your-org/wtms.git
cd wtms
```

#### 2.1.2 配置环境变量
```bash
# 复制环境变量模板
cp .env.example .env

# 编辑环境变量
vim .env
```

#### 2.1.3 启动服务
```bash
# 启动所有服务
docker-compose -f docker-compose.prod.yml up -d

# 查看服务状态
docker-compose -f docker-compose.prod.yml ps

# 查看日志
docker-compose -f docker-compose.prod.yml logs -f
```

#### 2.1.4 初始化数据库
```bash
# 执行数据库初始化脚本
docker-compose -f docker-compose.prod.yml exec backend \
  java -jar app.jar --spring.profiles.active=prod --init-database
```

### 2.2 手动部署

#### 2.2.1 数据库部署

**安装MySQL 8.0**
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install mysql-server-8.0

# CentOS/RHEL
sudo yum install mysql-server
sudo systemctl start mysqld
sudo systemctl enable mysqld
```

**创建数据库和用户**
```sql
-- 创建数据库
CREATE DATABASE wtms CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户
CREATE USER 'wtms'@'%' IDENTIFIED BY 'your_password';
GRANT ALL PRIVILEGES ON wtms.* TO 'wtms'@'%';
FLUSH PRIVILEGES;
```

**导入数据库结构**
```bash
mysql -u wtms -p wtms < wtms-backend/sql/00_execute_all.sql
```

**安装Redis**
```bash
# Ubuntu/Debian
sudo apt install redis-server

# CentOS/RHEL
sudo yum install redis
sudo systemctl start redis
sudo systemctl enable redis
```

#### 2.2.2 后端部署

**编译项目**
```bash
cd wtms-backend
mvn clean package -DskipTests
```

**配置应用**
```bash
# 复制配置文件
cp src/main/resources/application-prod.yml.example \
   src/main/resources/application-prod.yml

# 编辑生产配置
vim src/main/resources/application-prod.yml
```

**启动应用**
```bash
# 方式1：直接运行JAR
java -jar target/wtms-backend-1.0.0.jar --spring.profiles.active=prod

# 方式2：使用systemd服务
sudo cp scripts/wtms-backend.service /etc/systemd/system/
sudo systemctl daemon-reload
sudo systemctl start wtms-backend
sudo systemctl enable wtms-backend
```

#### 2.2.3 前端部署

**构建前端**
```bash
cd wtms-frontend
npm install
npm run build
```

**配置Nginx**
```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    # 前端静态文件
    location / {
        root /var/www/wtms/dist;
        try_files $uri $uri/ /index.html;
        
        # 缓存静态资源
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }
    
    # API代理
    location /api/ {
        proxy_pass http://localhost:55557;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 超时设置
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }
}
```

**部署静态文件**
```bash
sudo mkdir -p /var/www/wtms
sudo cp -r wtms-frontend/dist/* /var/www/wtms/
sudo chown -R www-data:www-data /var/www/wtms
```

## 3. 生产环境配置

### 3.1 数据库优化

#### 3.1.1 MySQL配置优化
```ini
# /etc/mysql/mysql.conf.d/mysqld.cnf
[mysqld]
# 基础配置
port = 3306
bind-address = 0.0.0.0
character-set-server = utf8mb4
collation-server = utf8mb4_unicode_ci

# 内存配置
innodb_buffer_pool_size = 2G
innodb_log_file_size = 256M
innodb_log_buffer_size = 64M
key_buffer_size = 256M

# 连接配置
max_connections = 1000
max_connect_errors = 100000
wait_timeout = 28800
interactive_timeout = 28800

# 性能配置
innodb_flush_log_at_trx_commit = 2
innodb_flush_method = O_DIRECT
innodb_file_per_table = 1
query_cache_type = 0
query_cache_size = 0

# 日志配置
slow_query_log = 1
slow_query_log_file = /var/log/mysql/slow.log
long_query_time = 2
log_queries_not_using_indexes = 1
```

#### 3.1.2 Redis配置优化
```conf
# /etc/redis/redis.conf
# 网络配置
bind 0.0.0.0
port 6379
timeout 300
tcp-keepalive 300

# 内存配置
maxmemory 1gb
maxmemory-policy allkeys-lru

# 持久化配置
save 900 1
save 300 10
save 60 10000
rdbcompression yes
rdbchecksum yes

# 安全配置
requirepass your_redis_password
rename-command FLUSHDB ""
rename-command FLUSHALL ""
rename-command CONFIG ""
```

### 3.2 应用配置

#### 3.2.1 JVM参数优化
```bash
# 生产环境JVM参数
JAVA_OPTS="-server \
  -Xms2g -Xmx2g \
  -XX:NewRatio=1 \
  -XX:SurvivorRatio=8 \
  -XX:+UseG1GC \
  -XX:MaxGCPauseMillis=200 \
  -XX:+UseStringDeduplication \
  -XX:+PrintGCDetails \
  -XX:+PrintGCTimeStamps \
  -Xloggc:/var/log/wtms/gc.log \
  -XX:+UseGCLogFileRotation \
  -XX:NumberOfGCLogFiles=5 \
  -XX:GCLogFileSize=10M \
  -Djava.awt.headless=true \
  -Dfile.encoding=UTF-8 \
  -Duser.timezone=Asia/Shanghai"

java $JAVA_OPTS -jar wtms-backend-1.0.0.jar
```

#### 3.2.2 应用配置文件
```yaml
# application-prod.yml
server:
  port: 55557
  tomcat:
    max-threads: 200
    min-spare-threads: 10
    max-connections: 8192
    accept-count: 100
    connection-timeout: 20000

spring:
  datasource:
    url: *************************************************************************
    username: ${DB_USERNAME:wtms}
    password: ${DB_PASSWORD:your_password}
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
      
  redis:
    host: ${REDIS_HOST:localhost}
    port: ${REDIS_PORT:6379}
    password: ${REDIS_PASSWORD:your_redis_password}
    timeout: 5000
    lettuce:
      pool:
        max-active: 20
        max-idle: 10
        min-idle: 5
        max-wait: 5000

logging:
  level:
    root: INFO
    com.wtms: INFO
  file:
    name: /var/log/wtms/application.log
    max-size: 100MB
    max-history: 30
```

### 3.3 安全配置

#### 3.3.1 防火墙配置
```bash
# Ubuntu UFW
sudo ufw allow 22/tcp    # SSH
sudo ufw allow 80/tcp    # HTTP
sudo ufw allow 443/tcp   # HTTPS
sudo ufw deny 3306/tcp   # MySQL (仅内网访问)
sudo ufw deny 6379/tcp   # Redis (仅内网访问)
sudo ufw enable

# CentOS firewalld
sudo firewall-cmd --permanent --add-service=ssh
sudo firewall-cmd --permanent --add-service=http
sudo firewall-cmd --permanent --add-service=https
sudo firewall-cmd --reload
```

#### 3.3.2 SSL证书配置
```nginx
server {
    listen 443 ssl http2;
    server_name your-domain.com;
    
    # SSL证书配置
    ssl_certificate /etc/ssl/certs/your-domain.crt;
    ssl_certificate_key /etc/ssl/private/your-domain.key;
    
    # SSL安全配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    
    # 安全头
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    add_header X-Frame-Options DENY always;
    add_header X-Content-Type-Options nosniff always;
    add_header X-XSS-Protection "1; mode=block" always;
    
    # 其他配置...
}

# HTTP重定向到HTTPS
server {
    listen 80;
    server_name your-domain.com;
    return 301 https://$server_name$request_uri;
}
```

## 4. 监控和维护

### 4.1 健康检查

#### 4.1.1 应用健康检查
```bash
# 检查应用状态
curl -f http://localhost:55557/api/v1/actuator/health

# 检查数据库连接
curl -f http://localhost:55557/api/v1/actuator/health/db

# 检查Redis连接
curl -f http://localhost:55557/api/v1/actuator/health/redis
```

#### 4.1.2 系统监控脚本
```bash
#!/bin/bash
# monitor.sh - 系统监控脚本

# 检查服务状态
check_service() {
    local service=$1
    if systemctl is-active --quiet $service; then
        echo "✅ $service is running"
    else
        echo "❌ $service is not running"
        systemctl restart $service
    fi
}

# 检查端口
check_port() {
    local port=$1
    local service=$2
    if netstat -tuln | grep -q ":$port "; then
        echo "✅ $service port $port is open"
    else
        echo "❌ $service port $port is not accessible"
    fi
}

# 检查磁盘空间
check_disk() {
    local usage=$(df / | awk 'NR==2 {print $5}' | sed 's/%//')
    if [ $usage -gt 80 ]; then
        echo "⚠️  Disk usage is ${usage}%"
    else
        echo "✅ Disk usage is ${usage}%"
    fi
}

# 执行检查
check_service mysql
check_service redis-server
check_service wtms-backend
check_service nginx

check_port 3306 MySQL
check_port 6379 Redis
check_port 55557 "WTMS Backend"
check_port 80 Nginx
check_port 443 "Nginx SSL"

check_disk
```

### 4.2 日志管理

#### 4.2.1 日志轮转配置
```bash
# /etc/logrotate.d/wtms
/var/log/wtms/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 wtms wtms
    postrotate
        systemctl reload wtms-backend
    endscript
}
```

### 4.3 备份策略

#### 4.3.1 数据库备份脚本
```bash
#!/bin/bash
# backup.sh - 数据库备份脚本

BACKUP_DIR="/backup/mysql"
DATE=$(date +%Y%m%d_%H%M%S)
DB_NAME="wtms"
DB_USER="wtms"
DB_PASS="your_password"

# 创建备份目录
mkdir -p $BACKUP_DIR

# 执行备份
mysqldump -u$DB_USER -p$DB_PASS \
  --single-transaction \
  --routines \
  --triggers \
  $DB_NAME > $BACKUP_DIR/wtms_$DATE.sql

# 压缩备份文件
gzip $BACKUP_DIR/wtms_$DATE.sql

# 删除7天前的备份
find $BACKUP_DIR -name "wtms_*.sql.gz" -mtime +7 -delete

echo "Database backup completed: wtms_$DATE.sql.gz"
```

## 5. 故障排除

### 5.1 常见问题

#### 5.1.1 应用启动失败
```bash
# 检查日志
tail -f /var/log/wtms/application.log

# 检查端口占用
netstat -tuln | grep 55557

# 检查Java进程
ps aux | grep java

# 检查配置文件
java -jar wtms-backend-1.0.0.jar --spring.config.location=application-prod.yml --debug
```

#### 5.1.2 数据库连接问题
```bash
# 测试数据库连接
mysql -h localhost -u wtms -p wtms

# 检查MySQL状态
systemctl status mysql

# 查看MySQL错误日志
tail -f /var/log/mysql/error.log
```

#### 5.1.3 Redis连接问题
```bash
# 测试Redis连接
redis-cli -h localhost -p 6379 -a your_password ping

# 检查Redis状态
systemctl status redis

# 查看Redis日志
tail -f /var/log/redis/redis-server.log
```

### 5.2 性能调优

#### 5.2.1 数据库性能分析
```sql
-- 查看慢查询
SELECT * FROM mysql.slow_log ORDER BY start_time DESC LIMIT 10;

-- 查看连接状态
SHOW PROCESSLIST;

-- 查看表状态
SHOW TABLE STATUS LIKE 'tasks';

-- 分析查询执行计划
EXPLAIN SELECT * FROM tasks WHERE status = 'IN_PROGRESS';
```

#### 5.2.2 应用性能监控
```bash
# JVM内存使用情况
jstat -gc <pid>

# 线程堆栈信息
jstack <pid>

# 堆内存分析
jmap -histo <pid>
```

---

**文档版本**: v1.0.0  
**最后更新**: 2024-07-28  
**维护团队**: WTMS开发团队
