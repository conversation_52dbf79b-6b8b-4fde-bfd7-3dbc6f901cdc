# WTMS 工作任务管理系统架构设计文档

## 1. 系统概述

### 1.1 项目简介
WTMS（Work Task Management System）是一个企业级工作任务管理平台，采用前后端分离架构，为企业提供高效的任务管理、项目协作和工作流程管理解决方案。

### 1.2 技术栈
- **前端**: Vue 3 + TypeScript + Vite + Element Plus + Pinia
- **后端**: Spring Boot 2.7.18 + Spring Security + MyBatis Plus
- **数据库**: MySQL 8.0 + Redis 7.0
- **构建工具**: Maven 3.6+ + Node.js 18+
- **容器化**: Docker + Docker Compose

### 1.3 系统特性
- 🔐 基于JWT的身份认证和RBAC权限控制
- 📊 实时任务状态跟踪和数据统计
- 🔄 灵活的工作流程配置
- 📁 安全的文件上传和管理
- 🎨 响应式UI设计，支持多设备访问
- 🛡️ 完善的安全防护机制

## 2. 系统架构

### 2.1 整体架构图

```
┌─────────────────────────────────────────────────────────────┐
│                        用户层                                │
├─────────────────────────────────────────────────────────────┤
│  Web浏览器  │  移动端  │  桌面应用  │  第三方系统集成        │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                      前端层 (Vue 3)                         │
├─────────────────────────────────────────────────────────────┤
│  路由管理  │  状态管理  │  组件库  │  工具函数  │  API调用   │
│ Vue Router │   Pinia   │Element+│  Utils   │   Axios     │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼ HTTP/HTTPS
┌─────────────────────────────────────────────────────────────┐
│                    API网关层 (可选)                          │
├─────────────────────────────────────────────────────────────┤
│  负载均衡  │  API限流  │  安全过滤  │  监控日志  │  缓存     │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                   后端服务层 (Spring Boot)                   │
├─────────────────────────────────────────────────────────────┤
│  Controller │  Service  │  Security │  Config  │  Utils    │
│   控制层     │  业务层   │  安全层   │  配置层  │  工具层    │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                     数据访问层                               │
├─────────────────────────────────────────────────────────────┤
│  MyBatis Plus │  数据源管理  │  事务管理  │  连接池        │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                      数据存储层                              │
├─────────────────────────────────────────────────────────────┤
│     MySQL 8.0     │      Redis 7.0      │   文件存储       │
│   (主数据库)       │    (缓存/会话)      │  (附件/日志)     │
└─────────────────────────────────────────────────────────────┘
```

### 2.2 分层架构详解

#### 2.2.1 前端架构 (Vue 3)
```
src/
├── components/          # 公共组件
│   ├── common/         # 通用组件
│   ├── business/       # 业务组件
│   └── layout/         # 布局组件
├── views/              # 页面视图
│   ├── auth/          # 认证相关
│   ├── dashboard/     # 仪表板
│   ├── tasks/         # 任务管理
│   ├── projects/      # 项目管理
│   └── system/        # 系统管理
├── stores/             # Pinia状态管理
├── router/             # 路由配置
├── api/                # API接口
├── utils/              # 工具函数
├── assets/             # 静态资源
└── styles/             # 样式文件
```

#### 2.2.2 后端架构 (Spring Boot)
```
src/main/java/com/wtms/
├── controller/         # 控制器层
│   ├── auth/          # 认证控制器
│   ├── task/          # 任务控制器
│   ├── project/       # 项目控制器
│   └── system/        # 系统控制器
├── service/            # 服务层
│   ├── impl/          # 服务实现
│   └── interfaces/    # 服务接口
├── entity/             # 实体类
├── mapper/             # 数据访问层
├── dto/                # 数据传输对象
├── vo/                 # 视图对象
├── config/             # 配置类
├── security/           # 安全相关
├── common/             # 公共类
│   ├── constants/     # 常量
│   ├── enums/         # 枚举
│   ├── exception/     # 异常处理
│   └── utils/         # 工具类
└── WtmsQuickStartApp.java  # 启动类
```

## 3. 核心模块设计

### 3.1 认证授权模块

#### 3.1.1 JWT认证流程
```mermaid
sequenceDiagram
    participant C as Client
    participant A as Auth Controller
    participant S as Auth Service
    participant U as User Service
    participant R as Redis
    participant D as Database

    C->>A: 登录请求 (username/password)
    A->>S: 验证用户凭据
    S->>U: 查询用户信息
    U->>D: 数据库查询
    D-->>U: 用户数据
    U-->>S: 用户信息
    S->>S: 密码验证
    S->>S: 生成JWT Token
    S->>R: 存储Token信息
    S-->>A: 认证结果
    A-->>C: 返回Token

    Note over C,D: 后续请求携带Token
    C->>A: API请求 (Header: Authorization)
    A->>S: Token验证
    S->>R: 检查Token状态
    R-->>S: Token信息
    S-->>A: 验证结果
    A->>A: 业务处理
    A-->>C: 响应结果
```

#### 3.1.2 RBAC权限模型
```
用户 (User) ←→ 用户角色 (UserRole) ←→ 角色 (Role)
                                        ↓
                                   角色权限 (RolePermission)
                                        ↓
                                   权限 (Permission)
```

### 3.2 任务管理模块

#### 3.2.1 任务状态流转
```mermaid
stateDiagram-v2
    [*] --> 待开始
    待开始 --> 进行中: 开始任务
    进行中 --> 已暂停: 暂停任务
    已暂停 --> 进行中: 恢复任务
    进行中 --> 已完成: 完成任务
    进行中 --> 已取消: 取消任务
    待开始 --> 已取消: 取消任务
    已完成 --> [*]
    已取消 --> [*]
```

#### 3.2.2 任务数据模型
```sql
-- 任务表
CREATE TABLE tasks (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    title VARCHAR(255) NOT NULL COMMENT '任务标题',
    description TEXT COMMENT '任务描述',
    status ENUM('PENDING', 'IN_PROGRESS', 'PAUSED', 'COMPLETED', 'CANCELLED') DEFAULT 'PENDING',
    priority ENUM('LOW', 'MEDIUM', 'HIGH', 'URGENT') DEFAULT 'MEDIUM',
    assignee_id BIGINT COMMENT '执行人ID',
    creator_id BIGINT NOT NULL COMMENT '创建人ID',
    project_id BIGINT COMMENT '所属项目ID',
    parent_task_id BIGINT COMMENT '父任务ID',
    start_date DATETIME COMMENT '开始时间',
    due_date DATETIME COMMENT '截止时间',
    completed_at DATETIME COMMENT '完成时间',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted_at DATETIME COMMENT '软删除时间',
    INDEX idx_assignee (assignee_id),
    INDEX idx_creator (creator_id),
    INDEX idx_project (project_id),
    INDEX idx_status (status),
    INDEX idx_priority (priority)
);
```

## 4. 安全架构

### 4.1 安全防护层次

#### 4.1.1 网络安全
- HTTPS加密传输
- CORS跨域配置
- 安全响应头设置
- API访问频率限制

#### 4.1.2 应用安全
- JWT Token认证
- RBAC权限控制
- 输入参数验证
- SQL注入防护
- XSS攻击防护

#### 4.1.3 数据安全
- 敏感数据加密存储
- 数据库连接加密
- 审计日志记录
- 数据备份策略

### 4.2 文件上传安全

#### 4.2.1 安全检查流程
```mermaid
flowchart TD
    A[文件上传] --> B[文件大小检查]
    B --> C[文件类型检查]
    C --> D[MIME类型验证]
    D --> E[文件名安全检查]
    E --> F[文件内容魔数验证]
    F --> G[恶意内容扫描]
    G --> H[病毒扫描]
    H --> I[生成文件哈希]
    I --> J[存储到安全目录]
    
    B -->|超出限制| K[拒绝上传]
    C -->|类型不允许| K
    D -->|MIME不匹配| K
    E -->|文件名危险| K
    F -->|内容不匹配| K
    G -->|发现恶意内容| K
    H -->|发现病毒| K
```

## 5. 数据库设计

### 5.1 核心表结构

#### 5.1.1 用户权限相关表
```sql
-- 用户表
users (id, username, password, email, status, created_at, updated_at)

-- 角色表  
roles (id, name, code, description, status, created_at, updated_at)

-- 权限表
permissions (id, name, code, resource, action, description, created_at, updated_at)

-- 用户角色关联表
user_roles (id, user_id, role_id, status, assigned_at, expires_at)

-- 角色权限关联表
role_permissions (id, role_id, permission_id, created_at)
```

#### 5.1.2 业务核心表
```sql
-- 项目表
projects (id, name, description, status, manager_id, created_at, updated_at)

-- 任务表
tasks (id, title, description, status, priority, assignee_id, creator_id, project_id, ...)

-- 任务评论表
task_comments (id, task_id, user_id, content, created_at)

-- 文件附件表
attachments (id, filename, original_name, file_path, file_size, mime_type, hash, ...)
```

### 5.2 索引策略
- 主键索引：所有表的id字段
- 唯一索引：用户名、邮箱等唯一字段
- 复合索引：常用查询条件组合
- 外键索引：关联查询优化

## 6. 缓存架构

### 6.1 Redis缓存策略

#### 6.1.1 缓存分层
```
L1: 应用内存缓存 (Caffeine)
    ↓ (未命中)
L2: Redis分布式缓存
    ↓ (未命中)  
L3: 数据库查询
```

#### 6.1.2 缓存数据类型
- **用户会话**: `session:{userId}` (Hash, TTL: 30min)
- **用户权限**: `permissions:{userId}` (Set, TTL: 1hour)
- **热点数据**: `hotdata:{type}:{id}` (String/Hash, TTL: 5min)
- **计数器**: `counter:{type}:{date}` (String, TTL: 1day)

## 7. 监控与运维

### 7.1 监控指标
- **系统指标**: CPU、内存、磁盘、网络
- **应用指标**: QPS、响应时间、错误率
- **业务指标**: 用户活跃度、任务完成率
- **数据库指标**: 连接数、慢查询、锁等待

### 7.2 日志管理
- **访问日志**: Nginx/Tomcat访问日志
- **应用日志**: 业务操作日志、错误日志
- **审计日志**: 敏感操作记录
- **性能日志**: 慢查询、长时间操作

### 7.3 部署架构
```
┌─────────────────────────────────────────────────────────────┐
│                      负载均衡器                              │
│                    (Nginx/HAProxy)                          │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                    Web服务器集群                             │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │
│  │   Node 1    │  │   Node 2    │  │   Node 3    │        │
│  │ Spring Boot │  │ Spring Boot │  │ Spring Boot │        │
│  └─────────────┘  └─────────────┘  └─────────────┘        │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                     数据库集群                               │
│  ┌─────────────┐              ┌─────────────┐              │
│  │   Master    │◄────────────►│   Slave     │              │
│  │   MySQL     │              │   MySQL     │              │
│  └─────────────┘              └─────────────┘              │
│                                                             │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │
│  │  Redis-1    │  │  Redis-2    │  │  Redis-3    │        │
│  │  (Master)   │  │  (Slave)    │  │ (Sentinel)  │        │
│  └─────────────┘  └─────────────┘  └─────────────┘        │
└─────────────────────────────────────────────────────────────┘
```

## 8. 性能优化

### 8.1 前端优化
- **代码分割**: 路由懒加载、组件按需加载
- **资源优化**: 图片压缩、静态资源CDN
- **缓存策略**: 浏览器缓存、Service Worker
- **构建优化**: Tree Shaking、代码压缩

### 8.2 后端优化
- **数据库优化**: 索引优化、查询优化、连接池调优
- **缓存优化**: 多级缓存、缓存预热、缓存更新策略
- **异步处理**: 消息队列、异步任务
- **连接优化**: HTTP/2、Keep-Alive、连接复用

### 8.3 系统优化
- **负载均衡**: 请求分发、健康检查
- **水平扩展**: 微服务拆分、数据库分片
- **监控告警**: 实时监控、自动告警、故障自愈

## 9. 扩展性设计

### 9.1 微服务演进路径
```
单体应用 → 模块化单体 → 微服务架构

当前阶段: 模块化单体
未来规划: 
- 用户服务 (User Service)
- 任务服务 (Task Service)  
- 项目服务 (Project Service)
- 通知服务 (Notification Service)
- 文件服务 (File Service)
```

### 9.2 API版本管理
- URL版本控制: `/api/v1/`, `/api/v2/`
- 向后兼容性保证
- 废弃API生命周期管理

### 9.3 插件化架构
- 业务插件接口定义
- 插件生命周期管理
- 插件配置和热加载

## 10. API设计规范

### 10.1 RESTful API设计原则
- **资源导向**: URL表示资源，HTTP方法表示操作
- **统一接口**: 标准化的请求/响应格式
- **无状态**: 每个请求包含完整的处理信息
- **可缓存**: 支持HTTP缓存机制

### 10.2 API路径规范
```
GET    /api/v1/tasks           # 获取任务列表
GET    /api/v1/tasks/{id}      # 获取单个任务
POST   /api/v1/tasks           # 创建任务
PUT    /api/v1/tasks/{id}      # 更新任务
DELETE /api/v1/tasks/{id}      # 删除任务
PATCH  /api/v1/tasks/{id}      # 部分更新任务
```

### 10.3 统一响应格式
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    // 业务数据
  },
  "timestamp": "2024-07-28T10:30:00Z",
  "traceId": "abc123def456"
}
```

### 10.4 错误码规范
- **2xx**: 成功响应
- **4xx**: 客户端错误
- **5xx**: 服务器错误

## 11. 开发规范

### 11.1 代码规范
- **Java**: Google Java Style Guide
- **TypeScript**: ESLint + Prettier
- **数据库**: 统一命名规范
- **注释**: 完整的JavaDoc和JSDoc

### 11.2 Git工作流
```
master (生产环境)
  ↑
develop (开发环境)
  ↑
feature/xxx (功能分支)
```

### 11.3 测试策略
- **单元测试**: 覆盖率 > 80%
- **集成测试**: 关键业务流程
- **端到端测试**: 用户场景验证
- **性能测试**: 压力测试和基准测试

---

**文档版本**: v1.0.0
**最后更新**: 2024-07-28
**维护团队**: WTMS开发团队
