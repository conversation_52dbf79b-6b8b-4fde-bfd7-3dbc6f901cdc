{"version": "0.2.0", "configurations": [{"type": "java", "name": "启动 WTMS 后端应用", "request": "launch", "mainClass": "com.wtms.WtmsQuickStartApp", "projectName": "wtms-backend", "args": "--spring.profiles.active=dev", "vmArgs": "-Dspring.profiles.active=dev -Dfile.encoding=UTF-8", "env": {"SPRING_PROFILES_ACTIVE": "dev"}, "console": "internalConsole", "internalConsoleOptions": "openOnSessionStart"}, {"type": "java", "name": "调试 WTMS 后端应用", "request": "launch", "mainClass": "com.wtms.WtmsQuickStartApp", "projectName": "wtms-backend", "args": "--spring.profiles.active=dev --debug", "vmArgs": "-Dspring.profiles.active=dev -Dfile.encoding=UTF-8 -Ddebug=true", "env": {"SPRING_PROFILES_ACTIVE": "dev", "DEBUG": "true"}, "console": "internalConsole", "internalConsoleOptions": "openOnSessionStart"}, {"type": "java", "name": "运行当前Java文件", "request": "launch", "mainClass": "${file}", "console": "internalConsole"}, {"type": "java", "name": "调试当前Java文件", "request": "launch", "mainClass": "${file}", "console": "internalConsole", "stopOnEntry": true}, {"name": "启动前端开发服务器", "type": "node-terminal", "request": "launch", "command": "npm run dev", "cwd": "${workspaceFolder}/wtms-frontend", "console": "integratedTerminal"}], "compounds": [{"name": "启动 WTMS 全栈应用", "configurations": ["启动 WTMS 后端应用", "启动前端开发服务器"], "stopAll": true, "presentation": {"hidden": false, "group": "WTMS", "order": 1}}]}