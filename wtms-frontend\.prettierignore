# 依赖目录
node_modules/
dist/
build/

# 构建输出
*.min.js
*.min.css

# 包管理文件
package-lock.json
yarn.lock
pnpm-lock.yaml

# 环境配置
.env
.env.local
.env.production

# 日志文件
*.log
logs/

# 临时文件
*.tmp
*.temp

# IDE配置
.vscode/
.idea/

# Git相关
.git/
.gitignore

# 自动生成的文件
auto-imports.d.ts
components.d.ts

# 文档
CHANGELOG.md
LICENSE

# 特定格式文件
*.svg
*.png
*.jpg
*.jpeg
*.gif
*.ico
*.woff
*.woff2
*.ttf
*.eot

# 已格式化的文件
*.min.*
*.bundle.*

# 第三方库
public/lib/
src/assets/lib/

# 配置文件（某些不需要格式化）
vite.config.ts
vitest.config.ts
tailwind.config.js
postcss.config.js
