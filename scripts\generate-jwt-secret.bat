@echo off
echo ================================
echo WTMS JWT密钥生成工具
echo ================================
echo.

echo 正在生成JWT密钥...
echo.

REM 生成随机Base64密钥
powershell -Command "$bytes = New-Object byte[] 32; (New-Object Random).NextBytes($bytes); $secret = [Convert]::ToBase64String($bytes); Write-Host '生成的JWT密钥:'; Write-Host 'JWT_SECRET='$secret; Write-Host ''; Write-Host '使用说明:'; Write-Host '1. 复制上述密钥'; Write-Host '2. 在.env文件中设置JWT_SECRET'; Write-Host '3. 确保.env文件已添加到.gitignore'; Write-Host '4. 生产环境使用不同的密钥'"

echo.
echo ================================
echo 安全提醒:
echo ================================
echo - 绝不要在代码中硬编码密钥
echo - 不要在版本控制中提交密钥  
echo - 生产环境和开发环境使用不同密钥
echo - 定期更换密钥
echo ================================

pause
