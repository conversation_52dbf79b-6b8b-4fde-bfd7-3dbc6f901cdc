{"version": "2.0.0", "tasks": [{"label": "WTMS: 启动前端开发服务器", "type": "shell", "command": "npm", "args": ["run", "dev"], "options": {"cwd": "${workspaceFolder}/wtms-frontend"}, "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new", "showReuseMessage": true, "clear": false}, "problemMatcher": [], "runOptions": {"runOn": "folderOpen"}}, {"label": "WTMS: 构建前端项目", "type": "shell", "command": "npm", "args": ["run", "build"], "options": {"cwd": "${workspaceFolder}/wtms-frontend"}, "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": []}, {"label": "WTMS: 启动后端Spring Boot应用", "type": "shell", "command": "mvn", "args": ["spring-boot:run"], "options": {"cwd": "${workspaceFolder}/wtms-backend"}, "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new", "showReuseMessage": true, "clear": false}, "problemMatcher": []}, {"label": "WTMS: 编译后端项目", "type": "shell", "command": "mvn", "args": ["clean", "compile"], "options": {"cwd": "${workspaceFolder}/wtms-backend"}, "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": []}, {"label": "WTMS: 运行后端测试", "type": "shell", "command": "mvn", "args": ["test"], "options": {"cwd": "${workspaceFolder}/wtms-backend"}, "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": []}, {"label": "WTMS: 安装前端依赖", "type": "shell", "command": "npm", "args": ["install"], "options": {"cwd": "${workspaceFolder}/wtms-frontend"}, "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": []}, {"label": "WTMS: 代码格式化", "type": "shell", "command": "npm", "args": ["run", "format"], "options": {"cwd": "${workspaceFolder}/wtms-frontend"}, "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": []}, {"label": "WTMS: ESLint检查", "type": "shell", "command": "npm", "args": ["run", "lint"], "options": {"cwd": "${workspaceFolder}/wtms-frontend"}, "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": []}, {"label": "WTMS: 生成JWT密钥", "type": "shell", "command": "node", "args": ["scripts/generate-jwt-secret.js"], "options": {"cwd": "${workspaceFolder}"}, "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": true, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": []}, {"label": "WTMS: 安全配置检查", "type": "shell", "command": "node", "args": ["scripts/validate-security-config.js"], "options": {"cwd": "${workspaceFolder}"}, "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": true, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": []}, {"label": "WTMS: 启动Docker服务", "type": "shell", "command": "docker-compose", "args": ["-f", "docker-compose.dev.yml", "up", "-d"], "options": {"cwd": "${workspaceFolder}"}, "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": []}, {"label": "WTMS: 停止Docker服务", "type": "shell", "command": "docker-compose", "args": ["-f", "docker-compose.dev.yml", "down"], "options": {"cwd": "${workspaceFolder}"}, "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": []}]}