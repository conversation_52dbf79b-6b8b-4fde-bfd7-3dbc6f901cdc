package com.wtms.security;

import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * 安全响应头过滤器
 * 为所有HTTP响应添加安全相关的响应头
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Component
public class SecurityHeadersFilter extends OncePerRequestFilter {

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, 
                                    FilterChain filterChain) throws ServletException, IOException {
        
        // 防止点击劫持攻击
        response.setHeader("X-Frame-Options", "DENY");
        
        // 防止MIME类型嗅探
        response.setHeader("X-Content-Type-Options", "nosniff");
        
        // XSS保护
        response.setHeader("X-XSS-Protection", "1; mode=block");
        
        // 严格传输安全 (HSTS)
        if (request.isSecure()) {
            response.setHeader("Strict-Transport-Security", 
                "max-age=31536000; includeSubDomains; preload");
        }
        
        // 内容安全策略 (CSP)
        response.setHeader("Content-Security-Policy", 
            "default-src 'self'; " +
            "script-src 'self' 'unsafe-inline' 'unsafe-eval'; " +
            "style-src 'self' 'unsafe-inline'; " +
            "img-src 'self' data: https:; " +
            "font-src 'self' data:; " +
            "connect-src 'self'; " +
            "frame-ancestors 'none'; " +
            "base-uri 'self'; " +
            "form-action 'self'");
        
        // 引用者策略
        response.setHeader("Referrer-Policy", "strict-origin-when-cross-origin");
        
        // 权限策略
        response.setHeader("Permissions-Policy", 
            "geolocation=(), microphone=(), camera=(), payment=(), usb=(), " +
            "accelerometer=(), gyroscope=(), magnetometer=(), fullscreen=(self)");
        
        // 缓存控制
        if (request.getRequestURI().startsWith("/api/")) {
            response.setHeader("Cache-Control", "no-cache, no-store, must-revalidate");
            response.setHeader("Pragma", "no-cache");
            response.setHeader("Expires", "0");
        }
        
        // 隐藏服务器信息
        response.setHeader("Server", "WTMS");
        response.setHeader("X-Powered-By", "");
        
        // 防止信息泄露
        response.setHeader("X-Robots-Tag", "noindex, nofollow, nosnippet, noarchive");
        
        // 继续过滤器链
        filterChain.doFilter(request, response);
    }
    
    @Override
    protected boolean shouldNotFilter(HttpServletRequest request) throws ServletException {
        String path = request.getRequestURI();
        
        // 对于静态资源，可以跳过某些安全头
        return path.startsWith("/static/") || 
               path.startsWith("/public/") || 
               path.endsWith(".css") || 
               path.endsWith(".js") || 
               path.endsWith(".png") || 
               path.endsWith(".jpg") || 
               path.endsWith(".gif") || 
               path.endsWith(".ico") || 
               path.endsWith(".svg") || 
               path.endsWith(".woff") || 
               path.endsWith(".woff2") || 
               path.endsWith(".ttf");
    }
}
