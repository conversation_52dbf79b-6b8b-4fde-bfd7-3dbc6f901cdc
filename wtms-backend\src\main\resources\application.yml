server:
  port: 55557
  servlet:
    context-path: /api/v1
    encoding:
      charset: UTF-8
      enabled: true
      force: true

spring:
  application:
    name: wtms-backend

  profiles:
    active: dev

  main:
    allow-bean-definition-overriding: true

  # 文件上传配置
  servlet:
    multipart:
      enabled: true
      max-file-size: 10MB
      max-request-size: 50MB
      file-size-threshold: 1MB
      location: ${java.io.tmpdir}




  
  # 数据源配置
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: **********************************************************************************************************************************************
    username: ${DB_USERNAME:root}
    password: ${DB_PASSWORD:ankaixin.docker.mysql}
    
    # Druid连接池配置
    druid:
      initial-size: 5
      min-idle: 5
      max-active: 20
      max-wait: 60000
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      validation-query: SELECT 1
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 20
      # 监控配置
      stat-view-servlet:
        enabled: true
        url-pattern: /druid/*
        login-username: admin
        login-password: admin
      web-stat-filter:
        enabled: true
        url-pattern: /*
        exclusions: "*.js,*.gif,*.jpg,*.png,*.css,*.ico,/druid/*"
  
  # Redis配置
  redis:
    host: ${REDIS_HOST:localhost}
    port: ${REDIS_PORT:6379}
    password: ${REDIS_PASSWORD:}
    database: 0
    timeout: 3000ms
    lettuce:
      pool:
        max-active: 8
        max-idle: 8
        min-idle: 0
        max-wait: -1ms

  # Jackson配置
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    default-property-inclusion: non_null
    serialization:
      write-dates-as-timestamps: false
      fail-on-empty-beans: false
    deserialization:
      fail-on-unknown-properties: false



# MyBatis Plus配置
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      id-type: assign_uuid
      logic-delete-field: deleted_at
      logic-delete-value: now()
      logic-not-delete-value: 'null'
      insert-strategy: not_null
      update-strategy: not_null
      select-strategy: not_null
  mapper-locations: classpath*:/mapper/**/*.xml
  type-aliases-package: com.wtms.entity

# JWT配置
jwt:
  # JWT密钥 - 生产环境必须通过环境变量设置，不能使用默认值
  secret: ${JWT_SECRET:}
  expiration: ${JWT_EXPIRATION:86400000} # 24小时，单位：毫秒
  refresh-expiration: ${JWT_REFRESH_EXPIRATION:604800000} # 7天，单位：毫秒
  # JWT签名算法
  algorithm: ${JWT_ALGORITHM:HS256}
  # JWT发行者
  issuer: ${JWT_ISSUER:WTMS-System}
  # JWT受众
  audience: ${JWT_AUDIENCE:WTMS-Users}

# 日志配置
logging:
  level:
    com.wtms: debug
    org.springframework.security: debug
    org.springframework.web: debug
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/wtms.log
    max-size: 100MB
    max-history: 30

# SpringDoc配置
springdoc:
  api-docs:
    path: /v3/api-docs
  swagger-ui:
    path: /swagger-ui.html
    tags-sorter: alpha
    operations-sorter: alpha
  packages-to-scan: com.wtms.controller
  paths-to-match: /api/**

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,env
  endpoint:
    health:
      show-details: when-authorized
  info:
    env:
      enabled: true

# 应用信息
info:
  app:
    name: WTMS工作任务管理平台
    version: 1.0.0
    description: Work Task Management System Backend Service

# WTMS开发环境配置
wtms:
  development:
    enabled: true
    permission-override:
      enabled: true
      override-all: true
      override-roles: true
      override-permissions: true
      whitelist-users:
        - admin
        - developer
        - test
      blacklist-users: []
    super-admin:
      default-username: admin
      default-password: 123456
      auto-create: true
      role-code: ADMIN
      role-name: 超级管理员

  # 文件上传安全配置
  file-upload:
    max-file-size: 10MB
    max-request-size: 50MB
    file-size-threshold: 1MB
    temp-dir: ${java.io.tmpdir}
    upload-dir: uploads
    enable-file-type-check: true
    enable-mime-type-check: true
    enable-content-check: true
    max-filename-length: 255
    allowed-file-types:
      - jpg
      - jpeg
      - png
      - gif
      - bmp
      - webp
      - svg
      - pdf
      - doc
      - docx
      - xls
      - xlsx
      - ppt
      - pptx
      - txt
      - rtf
      - zip
      - rar
      - 7z
      - tar
      - gz
      - csv
      - json
      - xml
    forbidden-file-types:
      - exe
      - bat
      - cmd
      - com
      - pif
      - scr
      - vbs
      - js
      - jar
      - sh
      - bash
      - ps1
      - php
      - asp
      - aspx
      - jsp
      - dll
      - sys
      - ini
      - reg
      - msi
      - deb
      - rpm
      - dmg
      - iso
