#!/usr/bin/env node

/**
 * 安全配置验证脚本
 * 用于检查WTMS项目的安全配置
 * 
 * 使用方法:
 * node scripts/validate-security-config.js
 */

const fs = require('fs');
const path = require('path');

/**
 * 检查环境变量文件
 */
function checkEnvFiles() {
    const results = [];
    const envFiles = ['.env', '.env.local', '.env.production'];
    
    console.log('🔍 检查环境变量文件...');
    
    envFiles.forEach(file => {
        if (fs.existsSync(file)) {
            results.push({
                type: 'warning',
                message: `发现环境变量文件: ${file}`,
                recommendation: '确保此文件已添加到.gitignore中'
            });
        }
    });
    
    // 检查.env.example是否存在
    if (fs.existsSync('.env.example')) {
        results.push({
            type: 'success',
            message: '✅ .env.example文件存在'
        });
    } else {
        results.push({
            type: 'error',
            message: '❌ 缺少.env.example文件',
            recommendation: '创建.env.example作为环境变量模板'
        });
    }
    
    return results;
}

/**
 * 检查.gitignore文件
 */
function checkGitignore() {
    const results = [];
    
    console.log('🔍 检查.gitignore文件...');
    
    if (!fs.existsSync('.gitignore')) {
        results.push({
            type: 'error',
            message: '❌ 缺少.gitignore文件',
            recommendation: '创建.gitignore文件以保护敏感信息'
        });
        return results;
    }
    
    const gitignoreContent = fs.readFileSync('.gitignore', 'utf8');
    const requiredPatterns = [
        '.env',
        '*.log',
        'node_modules/',
        'target/',
        '.idea/',
        '.vscode/',
        '*.key',
        '*.pem'
    ];
    
    requiredPatterns.forEach(pattern => {
        if (gitignoreContent.includes(pattern)) {
            results.push({
                type: 'success',
                message: `✅ .gitignore包含: ${pattern}`
            });
        } else {
            results.push({
                type: 'warning',
                message: `⚠️  .gitignore缺少: ${pattern}`,
                recommendation: `添加 ${pattern} 到.gitignore`
            });
        }
    });
    
    return results;
}

/**
 * 检查JWT配置
 */
function checkJWTConfig() {
    const results = [];
    
    console.log('🔍 检查JWT配置...');
    
    const configFile = 'wtms-backend/src/main/resources/application.yml';
    if (!fs.existsSync(configFile)) {
        results.push({
            type: 'error',
            message: '❌ 找不到application.yml文件'
        });
        return results;
    }
    
    const configContent = fs.readFileSync(configFile, 'utf8');
    
    // 检查是否使用环境变量
    if (configContent.includes('${JWT_SECRET:}') || configContent.includes('${JWT_SECRET:')) {
        results.push({
            type: 'success',
            message: '✅ JWT密钥使用环境变量配置'
        });
    } else if (configContent.includes('secret:') && !configContent.includes('${')) {
        results.push({
            type: 'error',
            message: '❌ JWT密钥可能硬编码在配置文件中',
            recommendation: '使用环境变量 ${JWT_SECRET:} 配置JWT密钥'
        });
    }
    
    // 检查默认值
    if (configContent.includes('${JWT_SECRET:wtms-') || configContent.includes('${JWT_SECRET:default')) {
        results.push({
            type: 'warning',
            message: '⚠️  JWT密钥使用了默认值',
            recommendation: '生产环境必须设置强随机JWT密钥'
        });
    }
    
    return results;
}

/**
 * 检查敏感文件
 */
function checkSensitiveFiles() {
    const results = [];
    
    console.log('🔍 检查敏感文件...');
    
    const sensitivePatterns = [
        '**/*.key',
        '**/*.pem',
        '**/*.p12',
        '**/*.jks',
        '**/database.properties',
        '**/application-local.yml'
    ];
    
    // 简化检查，只检查常见位置
    const sensitiveFiles = [
        '.env',
        'wtms-backend/src/main/resources/application-local.yml',
        'wtms-backend/src/main/resources/database.properties'
    ];
    
    sensitiveFiles.forEach(file => {
        if (fs.existsSync(file)) {
            results.push({
                type: 'warning',
                message: `⚠️  发现敏感文件: ${file}`,
                recommendation: '确保此文件已添加到.gitignore并不会提交到版本控制'
            });
        }
    });
    
    return results;
}

/**
 * 检查密码策略
 */
function checkPasswordPolicy() {
    const results = [];
    
    console.log('🔍 检查密码策略...');
    
    // 检查是否有密码策略配置
    const envExample = '.env.example';
    if (fs.existsSync(envExample)) {
        const content = fs.readFileSync(envExample, 'utf8');
        
        const passwordPolicies = [
            'PASSWORD_MIN_LENGTH',
            'PASSWORD_REQUIRE_UPPERCASE',
            'PASSWORD_REQUIRE_LOWERCASE',
            'PASSWORD_REQUIRE_NUMBERS'
        ];
        
        passwordPolicies.forEach(policy => {
            if (content.includes(policy)) {
                results.push({
                    type: 'success',
                    message: `✅ 密码策略配置: ${policy}`
                });
            } else {
                results.push({
                    type: 'info',
                    message: `💡 建议添加密码策略: ${policy}`
                });
            }
        });
    }
    
    return results;
}

/**
 * 生成安全报告
 */
function generateSecurityReport() {
    console.log('🛡️  WTMS 安全配置验证报告');
    console.log('================================\n');
    
    const allResults = [
        ...checkEnvFiles(),
        ...checkGitignore(),
        ...checkJWTConfig(),
        ...checkSensitiveFiles(),
        ...checkPasswordPolicy()
    ];
    
    // 统计结果
    const stats = {
        success: allResults.filter(r => r.type === 'success').length,
        warning: allResults.filter(r => r.type === 'warning').length,
        error: allResults.filter(r => r.type === 'error').length,
        info: allResults.filter(r => r.type === 'info').length
    };
    
    // 显示结果
    allResults.forEach(result => {
        console.log(result.message);
        if (result.recommendation) {
            console.log(`   💡 建议: ${result.recommendation}`);
        }
        console.log();
    });
    
    // 显示统计
    console.log('📊 检查统计:');
    console.log(`✅ 通过: ${stats.success}`);
    console.log(`⚠️  警告: ${stats.warning}`);
    console.log(`❌ 错误: ${stats.error}`);
    console.log(`💡 建议: ${stats.info}`);
    
    // 安全评分
    const totalChecks = stats.success + stats.warning + stats.error;
    const score = totalChecks > 0 ? Math.round((stats.success / totalChecks) * 100) : 0;
    
    console.log(`\n🎯 安全评分: ${score}/100`);
    
    if (score >= 80) {
        console.log('🟢 安全配置良好');
    } else if (score >= 60) {
        console.log('🟡 安全配置需要改进');
    } else {
        console.log('🔴 安全配置存在严重问题，需要立即修复');
    }
    
    console.log('\n📋 下一步行动:');
    if (stats.error > 0) {
        console.log('1. 优先修复所有错误项');
    }
    if (stats.warning > 0) {
        console.log('2. 处理警告项以提高安全性');
    }
    console.log('3. 定期运行此脚本检查安全配置');
    console.log('4. 在生产部署前进行完整的安全审计');
}

// 运行安全检查
if (require.main === module) {
    generateSecurityReport();
}
