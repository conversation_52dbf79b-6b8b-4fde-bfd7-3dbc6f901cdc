# ================================
# WTMS 工作任务管理系统 .gitignore
# ================================

# ================================
# 操作系统生成的文件
# ================================
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
desktop.ini

# ================================
# Node.js 相关
# ================================
# 依赖目录
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# 运行时数据
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Grunt intermediate storage
.grunt

# Bower dependency directory
bower_components

# node-waf configuration
.lock-wscript

# Compiled binary addons
build/Release

# Dependency directories
jspm_packages/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional stylelint cache
.stylelintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache
.cache
.parcel-cache

# Next.js build output
.next

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
public

# Storybook build outputs
.out
.storybook-out

# Temporary folders
tmp/
temp/

# ================================
# Java 相关
# ================================
# Compiled class file
*.class

# Log file
*.log

# BlueJ files
*.ctxt

# Mobile Tools for Java (J2ME)
.mtj.tmp/

# Package Files
*.jar
*.war
*.nar
*.ear
*.zip
*.tar.gz
*.rar

# virtual machine crash logs
hs_err_pid*
replay_pid*

# Maven
target/
pom.xml.tag
pom.xml.releaseBackup
pom.xml.versionsBackup
pom.xml.next
release.properties
dependency-reduced-pom.xml
buildNumber.properties
.mvn/timing.properties
.mvn/wrapper/maven-wrapper.jar

# Gradle
.gradle
build/
!gradle/wrapper/gradle-wrapper.jar
!**/src/main/**/build/
!**/src/test/**/build/

# ================================
# Spring Boot 相关
# ================================
HELP.md
.gradle
build/
!gradle/wrapper/gradle-wrapper.jar
!**/src/main/**/build/
!**/src/test/**/build/

# ================================
# IDE 配置文件
# ================================
# IntelliJ IDEA
.idea/
*.iws
*.iml
*.ipr
out/
!**/src/main/**/out/
!**/src/test/**/out/

# Eclipse
.apt_generated
.classpath
.factorypath
.project
.settings
.springBeans
.sts4-cache
bin/
!**/src/main/**/bin/
!**/src/test/**/bin/

# NetBeans
/nbproject/private/
/nbbuild/
/dist/
/nbdist/
/.nb-gradle/

# VS Code
.vscode/
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
!.vscode/*.code-snippets

# Local History for Visual Studio Code
.history/

# Built Visual Studio Code Extensions
*.vsix

# ================================
# 环境配置文件
# ================================
# 环境变量文件
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# 数据库配置
database.properties
application-local.yml
application-local.properties

# ================================
# 日志文件
# ================================
logs/
*.log
log/
*.log.*

# ================================
# 缓存和临时文件
# ================================
# Redis dump file
dump.rdb

# 临时文件
*.tmp
*.temp
*.swp
*.swo
*~

# 备份文件
*.bak
*.backup

# ================================
# 构建输出
# ================================
# 前端构建输出
dist/
build/

# 后端构建输出
target/
out/

# ================================
# 测试相关
# ================================
# 测试覆盖率报告
coverage/
*.lcov
.nyc_output/

# 测试结果
test-results/
junit.xml

# ================================
# 文档生成
# ================================
# API文档生成
api-docs/
docs/build/

# ================================
# 数据库文件
# ================================
# SQLite
*.sqlite
*.sqlite3
*.db

# H2 Database
*.h2.db
*.trace.db

# ================================
# 上传文件
# ================================
uploads/
files/
attachments/

# ================================
# 证书和密钥文件
# ================================
*.pem
*.key
*.crt
*.p12
*.jks
*.keystore

# ================================
# 系统特定文件
# ================================
# Windows
Thumbs.db
ehthumbs.db
Desktop.ini

# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon?

# Linux
*~

# ================================
# 开发工具生成的文件
# ================================
# Webpack
.webpack/

# ESLint
.eslintcache

# Prettier
.prettierignore

# Stylelint
.stylelintcache

# ================================
# 容器相关
# ================================
# Docker
.dockerignore

# ================================
# 其他
# ================================
# 压缩文件
*.7z
*.dmg
*.gz
*.iso
*.jar
*.rar
*.tar
*.zip

# 编辑器临时文件
.vscode/
*.swp
*.swo
*~

# 系统生成的文件
.DS_Store
Thumbs.db
