package com.wtms.service;

import com.wtms.config.FileUploadConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

/**
 * 文件上传安全验证服务
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Service
@Slf4j
public class FileUploadSecurityService {

    @Autowired
    private FileUploadConfig fileUploadConfig;

    /**
     * 文件魔数映射表
     */
    private static final Map<String, byte[]> FILE_SIGNATURES = new HashMap<>();

    static {
        // 图片文件魔数
        FILE_SIGNATURES.put("jpg", new byte[]{(byte) 0xFF, (byte) 0xD8, (byte) 0xFF});
        FILE_SIGNATURES.put("jpeg", new byte[]{(byte) 0xFF, (byte) 0xD8, (byte) 0xFF});
        FILE_SIGNATURES.put("png", new byte[]{(byte) 0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A});
        FILE_SIGNATURES.put("gif", new byte[]{0x47, 0x49, 0x46, 0x38});
        FILE_SIGNATURES.put("bmp", new byte[]{0x42, 0x4D});
        
        // 文档文件魔数
        FILE_SIGNATURES.put("pdf", new byte[]{0x25, 0x50, 0x44, 0x46});
        FILE_SIGNATURES.put("doc", new byte[]{(byte) 0xD0, (byte) 0xCF, 0x11, (byte) 0xE0, (byte) 0xA1, (byte) 0xB1, 0x1A, (byte) 0xE1});
        FILE_SIGNATURES.put("docx", new byte[]{0x50, 0x4B, 0x03, 0x04});
        FILE_SIGNATURES.put("xls", new byte[]{(byte) 0xD0, (byte) 0xCF, 0x11, (byte) 0xE0, (byte) 0xA1, (byte) 0xB1, 0x1A, (byte) 0xE1});
        FILE_SIGNATURES.put("xlsx", new byte[]{0x50, 0x4B, 0x03, 0x04});
        
        // 压缩文件魔数
        FILE_SIGNATURES.put("zip", new byte[]{0x50, 0x4B, 0x03, 0x04});
        FILE_SIGNATURES.put("rar", new byte[]{0x52, 0x61, 0x72, 0x21, 0x1A, 0x07, 0x00});
        FILE_SIGNATURES.put("7z", new byte[]{0x37, 0x7A, (byte) 0xBC, (byte) 0xAF, 0x27, 0x1C});
        
        // 可执行文件魔数（用于检测和阻止）
        FILE_SIGNATURES.put("exe", new byte[]{0x4D, 0x5A});
        FILE_SIGNATURES.put("dll", new byte[]{0x4D, 0x5A});
    }

    /**
     * 验证上传文件的安全性
     */
    public FileValidationResult validateFile(MultipartFile file) {
        FileValidationResult result = new FileValidationResult();
        
        try {
            // 1. 基本验证
            if (file == null || file.isEmpty()) {
                result.setValid(false);
                result.addError("文件为空");
                return result;
            }

            // 2. 文件名验证
            String filename = file.getOriginalFilename();
            if (!fileUploadConfig.isFilenameValid(filename)) {
                result.setValid(false);
                result.addError("文件名无效或包含危险字符");
                return result;
            }

            // 3. 文件大小验证
            if (file.getSize() > fileUploadConfig.getMaxFileSize().toBytes()) {
                result.setValid(false);
                result.addError("文件大小超过限制: " + fileUploadConfig.getMaxFileSize());
                return result;
            }

            // 4. 文件类型验证
            if (!fileUploadConfig.isFileTypeAllowed(filename)) {
                result.setValid(false);
                result.addError("不支持的文件类型");
                return result;
            }

            // 5. MIME类型验证
            String mimeType = file.getContentType();
            if (!fileUploadConfig.isMimeTypeAllowed(mimeType)) {
                result.setValid(false);
                result.addError("不支持的MIME类型: " + mimeType);
                return result;
            }

            // 6. 文件内容验证（魔数检查）
            if (fileUploadConfig.isEnableContentCheck()) {
                if (!validateFileContent(file, filename)) {
                    result.setValid(false);
                    result.addError("文件内容与扩展名不匹配");
                    return result;
                }
            }

            // 7. 恶意文件检测
            if (containsMaliciousContent(file)) {
                result.setValid(false);
                result.addError("检测到恶意文件内容");
                return result;
            }

            result.setValid(true);
            result.setFileHash(calculateFileHash(file));
            result.setCleanFilename(sanitizeFilename(filename));
            
        } catch (Exception e) {
            log.error("文件验证过程中发生错误", e);
            result.setValid(false);
            result.addError("文件验证失败: " + e.getMessage());
        }

        return result;
    }

    /**
     * 验证文件内容（通过魔数）
     */
    private boolean validateFileContent(MultipartFile file, String filename) {
        try {
            String extension = getFileExtension(filename);
            if (extension == null) {
                return false;
            }

            byte[] expectedSignature = FILE_SIGNATURES.get(extension.toLowerCase());
            if (expectedSignature == null) {
                // 如果没有定义魔数，则跳过检查
                return true;
            }

            byte[] fileHeader = new byte[expectedSignature.length];
            try (InputStream is = file.getInputStream()) {
                int bytesRead = is.read(fileHeader);
                if (bytesRead < expectedSignature.length) {
                    return false;
                }
            }

            return Arrays.equals(fileHeader, expectedSignature);
            
        } catch (IOException e) {
            log.error("读取文件内容时发生错误", e);
            return false;
        }
    }

    /**
     * 检测恶意文件内容
     */
    private boolean containsMaliciousContent(MultipartFile file) {
        try (InputStream is = file.getInputStream()) {
            byte[] buffer = new byte[1024];
            int bytesRead = is.read(buffer);
            
            if (bytesRead > 0) {
                String content = new String(buffer, 0, bytesRead).toLowerCase();
                
                // 检测脚本标签
                String[] maliciousPatterns = {
                    "<script", "javascript:", "vbscript:", "onload=", "onerror=",
                    "<?php", "<%", "#!/bin/", "#!/usr/bin/",
                    "cmd.exe", "powershell", "bash", "sh"
                };
                
                for (String pattern : maliciousPatterns) {
                    if (content.contains(pattern)) {
                        log.warn("检测到恶意内容模式: {}", pattern);
                        return true;
                    }
                }
            }
            
        } catch (IOException e) {
            log.error("检测恶意内容时发生错误", e);
            return true; // 出错时保守处理，认为是恶意文件
        }
        
        return false;
    }

    /**
     * 计算文件哈希值
     */
    private String calculateFileHash(MultipartFile file) {
        try {
            MessageDigest md = MessageDigest.getInstance("SHA-256");
            try (InputStream is = file.getInputStream()) {
                byte[] buffer = new byte[8192];
                int bytesRead;
                while ((bytesRead = is.read(buffer)) != -1) {
                    md.update(buffer, 0, bytesRead);
                }
            }
            
            byte[] hash = md.digest();
            StringBuilder sb = new StringBuilder();
            for (byte b : hash) {
                sb.append(String.format("%02x", b));
            }
            return sb.toString();
            
        } catch (NoSuchAlgorithmException | IOException e) {
            log.error("计算文件哈希值时发生错误", e);
            return null;
        }
    }

    /**
     * 清理文件名
     */
    private String sanitizeFilename(String filename) {
        if (filename == null) {
            return "unknown";
        }
        
        // 移除危险字符
        String sanitized = filename.replaceAll("[^a-zA-Z0-9._-]", "_");
        
        // 限制长度
        if (sanitized.length() > fileUploadConfig.getMaxFilenameLength()) {
            String extension = getFileExtension(sanitized);
            String nameWithoutExt = sanitized.substring(0, sanitized.lastIndexOf('.'));
            int maxNameLength = fileUploadConfig.getMaxFilenameLength() - extension.length() - 1;
            sanitized = nameWithoutExt.substring(0, Math.min(nameWithoutExt.length(), maxNameLength)) + "." + extension;
        }
        
        return sanitized;
    }

    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String filename) {
        if (filename == null || filename.isEmpty()) {
            return null;
        }
        
        int lastDotIndex = filename.lastIndexOf('.');
        if (lastDotIndex == -1 || lastDotIndex == filename.length() - 1) {
            return null;
        }
        
        return filename.substring(lastDotIndex + 1);
    }

    /**
     * 文件验证结果类
     */
    public static class FileValidationResult {
        private boolean valid = false;
        private String fileHash;
        private String cleanFilename;
        private StringBuilder errors = new StringBuilder();

        public boolean isValid() {
            return valid;
        }

        public void setValid(boolean valid) {
            this.valid = valid;
        }

        public String getFileHash() {
            return fileHash;
        }

        public void setFileHash(String fileHash) {
            this.fileHash = fileHash;
        }

        public String getCleanFilename() {
            return cleanFilename;
        }

        public void setCleanFilename(String cleanFilename) {
            this.cleanFilename = cleanFilename;
        }

        public String getErrors() {
            return errors.toString();
        }

        public void addError(String error) {
            if (errors.length() > 0) {
                errors.append("; ");
            }
            errors.append(error);
        }
    }
}
