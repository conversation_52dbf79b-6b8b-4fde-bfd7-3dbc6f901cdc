{"printWidth": 100, "tabWidth": 2, "useTabs": false, "semi": false, "singleQuote": true, "quoteProps": "as-needed", "jsxSingleQuote": true, "trailingComma": "es5", "bracketSpacing": true, "bracketSameLine": false, "arrowParens": "avoid", "rangeStart": 0, "rangeEnd": null, "requirePragma": false, "insertPragma": false, "proseWrap": "preserve", "htmlWhitespaceSensitivity": "css", "vueIndentScriptAndStyle": false, "endOfLine": "lf", "embeddedLanguageFormatting": "auto", "singleAttributePerLine": false, "overrides": [{"files": "*.vue", "options": {"parser": "vue", "printWidth": 100, "singleQuote": true, "semi": false, "trailingComma": "es5"}}, {"files": "*.json", "options": {"parser": "json", "printWidth": 100, "tabWidth": 2, "trailingComma": "none"}}, {"files": "*.md", "options": {"parser": "markdown", "printWidth": 80, "proseWrap": "always", "tabWidth": 2}}, {"files": "*.{css,scss,less}", "options": {"parser": "css", "printWidth": 100, "singleQuote": true, "tabWidth": 2}}, {"files": "*.{yaml,yml}", "options": {"parser": "yaml", "printWidth": 100, "tabWidth": 2, "singleQuote": false}}]}