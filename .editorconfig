# EditorConfig is awesome: https://EditorConfig.org

# top-most EditorConfig file
root = true

# 所有文件的默认设置
[*]
charset = utf-8
end_of_line = lf
insert_final_newline = true
trim_trailing_whitespace = true
indent_style = space
indent_size = 2

# Java文件
[*.java]
indent_size = 4
max_line_length = 120

# XML文件 (Maven <PERSON>OM, Spring配置等)
[*.{xml,pom}]
indent_size = 2
max_line_length = 120

# YAML文件 (Spring Boot配置等)
[*.{yml,yaml}]
indent_size = 2
max_line_length = 120

# JSON文件
[*.json]
indent_size = 2
max_line_length = 120

# JavaScript/TypeScript文件
[*.{js,jsx,ts,tsx}]
indent_size = 2
max_line_length = 100

# Vue文件
[*.vue]
indent_size = 2
max_line_length = 100

# CSS/SCSS文件
[*.{css,scss,sass,less}]
indent_size = 2
max_line_length = 100

# HTML文件
[*.html]
indent_size = 2
max_line_length = 120

# Markdown文件
[*.md]
trim_trailing_whitespace = false
max_line_length = 80

# 配置文件
[*.{properties,conf,config}]
indent_size = 2

# Shell脚本
[*.{sh,bash}]
indent_size = 2
end_of_line = lf

# 批处理文件
[*.{bat,cmd}]
end_of_line = crlf

# SQL文件
[*.sql]
indent_size = 2
max_line_length = 120

# Docker文件
[{Dockerfile,Dockerfile.*}]
indent_size = 2

# 忽略某些文件的尾随空格清理
[*.{md,txt}]
trim_trailing_whitespace = false

# 包管理文件
[{package.json,package-lock.json,yarn.lock,pnpm-lock.yaml}]
indent_size = 2

# Maven Wrapper
[{mvnw,mvnw.cmd}]
end_of_line = lf

# Gradle文件
[*.{gradle,gradle.kts}]
indent_size = 4
