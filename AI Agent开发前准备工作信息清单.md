# AI Agent开发前准备工作信息清单

## 📋 使用说明

**目标受众**: AI Agent开发助手  
**使用时机**: 项目开发启动前的信息收集阶段  
**完成标准**: 所有必填项(必填)收集完整，可选项(可选)根据项目需要收集  

> **重要提示**: 本清单基于能力模型工厂系统的实际开发经验制定，重点关注容易被忽视但影响重大的信息项。请确保在开始编码前完成所有信息收集。

---

## 🛠️ 一、开发工具栈信息收集

### 1.1 IDE和编辑器配置
| 信息项 | 优先级 | 格式要求 | 示例 | 人工确认点 |
|--------|--------|----------|------|------------|
| 主要IDE | 必填 | IDE名称 + 版本 | IntelliJ IDEA 2023.3 | ✅ **需人工确认团队统一IDE** |
| 必装扩展清单 | 必填 | 扩展名称列表 | Java Extension Pack, Spring Boot Tools | ✅ **需人工提供扩展清单** |
| 代码格式化配置 | 必填 | 配置文件路径 | `.editorconfig`, `.prettierrc` | ✅ **需人工提供格式化规则** |
| 备用编辑器 | 可选 | 编辑器名称 | VS Code, Eclipse | - |

**⚠️ 预防提示**: 
- 避免团队成员使用不同IDE导致配置文件冲突
- 确保所有必装扩展都有明确的版本要求

### 1.2 版本控制工具
| 信息项 | 优先级 | 格式要求 | 示例 | 人工确认点 |
|--------|--------|----------|------|------------|
| Git版本要求 | 必填 | 版本号 | >= 2.30.0 | - |
| 分支策略 | 必填 | 策略名称 | Git Flow, GitHub Flow | ✅ **需人工确认分支管理策略** |
| 提交规范 | 必填 | 规范格式 | Conventional Commits | ✅ **需人工确认提交信息格式** |
| Git客户端 | 可选 | 客户端名称 | SourceTree, GitKraken | - |

### 1.3 容器化工具
| 信息项 | 优先级 | 格式要求 | 示例 | 人工确认点 |
|--------|--------|----------|------|------------|
| 是否使用容器化 | 必填 | 是/否 | 是 | ✅ **需人工决策是否容器化开发** |
| 容器工具 | 必填 | 工具名称+版本 | Docker Desktop 4.15+ | ✅ **需人工选择容器化工具** |
| 基础镜像要求 | 可选 | 镜像名称 | openjdk:11-jre-slim | - |

---

## 🏗️ 二、技术堆栈规范信息收集

### 2.1 技术选型矩阵
| 层级 | 优先级 | 技术选项 | 版本要求 | 强制标准 | 人工确认点 |
|------|--------|----------|----------|----------|------------|
| 前端框架 | 必填 | Vue.js/React/Angular | 具体版本号 | ES2020+ | ✅ **需人工选择前端技术栈** |
| 后端框架 | 必填 | Spring Boot/Express/Django | 具体版本号 | REST API规范 | ✅ **需人工选择后端框架** |
| 数据库 | 必填 | MySQL/PostgreSQL/MongoDB | 具体版本号 | UTF-8编码 | ✅ **需人工选择数据库类型** |
| 缓存中间件 | 可选 | Redis/Memcached | 具体版本号 | TLS加密 | ✅ **需人工决定是否使用缓存** |

**⚠️ 预防提示**:
- 避免选择已停止维护的版本（如Spring Boot 2.7）
- 确保所有技术栈版本兼容性
- 明确编码规范和API设计标准

### 2.2 编程语言规范
| 信息项 | 优先级 | 格式要求 | 示例 | 人工确认点 |
|--------|--------|----------|------|------------|
| 主要编程语言 | 必填 | 语言+版本 | Java 11, TypeScript 4.9 | ✅ **需人工确认语言版本** |
| 代码风格指南 | 必填 | 指南名称 | Google Java Style Guide | ✅ **需人工选择代码风格** |
| 静态分析工具 | 必填 | 工具名称 | SonarQube, ESLint | ✅ **需人工配置分析工具** |
| 单元测试框架 | 必填 | 框架名称 | JUnit 5, Jest | - |

---

## 🌐 三、环境与依赖信息收集

### 3.1 环境变量配置
| 信息项 | 优先级 | 格式要求 | 示例 | 人工确认点 |
|--------|--------|----------|------|------------|
| 环境变量清单 | 必填 | KEY=VALUE格式 | `DB_HOST=localhost` | ✅ **需人工提供所有环境变量** |
| 敏感信息管理 | 必填 | 管理方式 | HashiCorp Vault, AWS Secrets | ✅ **需人工选择密钥管理方案** |
| 环境区分策略 | 必填 | 环境名称 | dev, test, staging, prod | ✅ **需人工确认环境划分** |
| .env模板文件 | 必填 | 文件路径 | `.env.example` | ✅ **需人工提供环境变量模板** |

**⚠️ 预防提示**:
- 禁止在代码中硬编码敏感信息
- 确保所有环境变量都有默认值或验证机制
- 使用环境变量插值语法：`${ENV_VAR}`

### 3.2 依赖管理配置
| 信息项 | 优先级 | 格式要求 | 示例 | 人工确认点 |
|--------|--------|----------|------|------------|
| 包管理工具 | 必填 | 工具名称+版本 | Maven 3.8+, npm 9.0+ | - |
| 依赖锁定策略 | 必填 | 锁定方式 | 精确版本锁定 | ✅ **需人工确认版本锁定策略** |
| 私有仓库配置 | 可选 | 仓库地址 | nexus.company.com | ✅ **需人工提供私有仓库信息** |
| 依赖安全扫描 | 必填 | 扫描工具 | OWASP Dependency Check | ✅ **需人工配置安全扫描** |

### 3.3 运行时环境
| 信息项 | 优先级 | 格式要求 | 示例 | 人工确认点 |
|--------|--------|----------|------|------------|
| JDK/Node.js版本 | 必填 | 版本号 | JDK 11, Node.js 18+ | ✅ **需人工确认运行时版本** |
| 虚拟环境工具 | 必填 | 工具名称 | conda, venv, nvm | ✅ **需人工选择虚拟环境方案** |
| 系统兼容性 | 必填 | 操作系统 | Windows 10+, Ubuntu 20.04+ | ✅ **需人工确认支持的操作系统** |

---

## 🏢 四、基础设施配置信息收集

### 4.1 数据库配置
| 信息项 | 优先级 | 格式要求 | 示例 | 人工确认点 |
|--------|--------|----------|------|------------|
| 数据库类型和版本 | 必填 | 类型+版本 | MySQL 8.0, PostgreSQL 15 | ✅ **需人工选择数据库** |
| 端口分配 | 必填 | 端口号 | 开发:3306, 生产:动态 | ✅ **需人工分配端口** |
| 连接池配置 | 必填 | 参数设置 | 最大连接数=50 | ✅ **需人工配置连接池参数** |
| 初始化脚本位置 | 必填 | 文件路径 | `/scripts/db_init.sql` | ✅ **需人工提供初始化脚本** |
| 字符集编码 | 必填 | 编码格式 | UTF-8 | - |

**⚠️ 预防提示**:
- 避免使用默认端口在生产环境
- 确保数据库连接池参数适合预期负载
- 所有SQL脚本必须支持幂等执行

### 4.2 网络配置
| 信息项 | 优先级 | 格式要求 | 示例 | 人工确认点 |
|--------|--------|----------|------|------------|
| 服务端口分配表 | 必填 | 服务:端口 | 前端:5173, 后端:8080 | ✅ **需人工分配所有服务端口** |
| CORS策略 | 必填 | 域名白名单 | localhost:*, *.company.com | ✅ **需人工配置跨域策略** |
| 防火墙规则 | 必填 | 端口开放清单 | 8080, 3306, 6379 | ✅ **需人工确认防火墙配置** |
| 负载均衡配置 | 可选 | 负载策略 | Round Robin, Least Connections | ✅ **需人工选择负载均衡策略** |

### 4.3 监控和日志
| 信息项 | 优先级 | 格式要求 | 示例 | 人工确认点 |
|--------|--------|----------|------|------------|
| 日志级别配置 | 必填 | 级别设置 | dev:DEBUG, prod:INFO | ✅ **需人工设置日志级别** |
| 日志存储位置 | 必填 | 路径 | `/logs/app.log` | ✅ **需人工指定日志路径** |
| 监控工具 | 可选 | 工具名称 | Prometheus, Grafana | ✅ **需人工选择监控方案** |
| 告警配置 | 可选 | 告警规则 | CPU>80%, 内存>90% | ✅ **需人工设置告警阈值** |

### 4.4 开发环境账户和凭证信息收集
| 信息项 | 优先级 | 格式要求 | 示例 | 人工确认点 |
|--------|--------|----------|------|------------|
| 数据库连接凭证 | 必填 | 🔒敏感信息 | `username=dev_user, password=*****, host=localhost:3306` | ✅ **需人工提供数据库凭证** |
| 应用管理员账户 | 必填 | 🔒敏感信息 | `admin/admin123, test_user/test123` | ✅ **需人工创建管理员账户** |
| JWT签名密钥 | 必填 | 🔒敏感信息 | `HS256密钥, 最少32位随机字符串` | ✅ **需人工生成JWT密钥** |
| Redis连接信息 | 可选 | 连接配置 | `host=localhost:6379, password=redis_pass` | ✅ **需人工提供Redis凭证** |
| 第三方API密钥 | 可选 | 🔒敏感信息 | `支付接口key, 短信服务key, 云存储key` | ✅ **需人工提供第三方密钥** |
| Git仓库访问凭证 | 必填 | 访问令牌 | `Personal Access Token, SSH Key` | ✅ **需人工配置Git访问权限** |
| CI/CD平台凭证 | 可选 | 平台账户 | `Jenkins用户, GitLab CI Token` | ✅ **需人工配置CI/CD访问** |
| 监控系统凭证 | 可选 | 监控账户 | `Grafana admin, Prometheus配置` | ✅ **需人工配置监控访问** |

**🔒 安全管理要求**:
- **敏感信息存储**: 使用环境变量或密钥管理服务，禁止硬编码
- **访问权限控制**: 开发环境使用受限权限账户，生产环境严格权限控制
- **凭证轮换策略**: 定期更换密码和密钥，建议90天轮换周期
- **传输安全**: 所有凭证传输必须使用加密通道(HTTPS/TLS)

**⚠️ 预防提示**:
- 开发环境和生产环境必须使用不同的凭证
- 所有默认密码必须在首次使用时更改
- 建立凭证泄露应急响应流程
- 定期审计账户使用情况和权限分配

---

## 🔐 五、权限矩阵信息收集

### 5.1 用户角色定义
| 角色名称 | 优先级 | 权限范围 | 数据访问级别 | 人工确认点 |
|----------|--------|----------|--------------|------------|
| 超级管理员 | 必填 | 全部功能 | 全部数据 | ✅ **需人工定义管理员权限** |
| 普通用户 | 必填 | 基础功能 | 个人数据 | ✅ **需人工定义用户权限** |
| 只读用户 | 可选 | 查看功能 | 只读访问 | ✅ **需人工确认是否需要只读角色** |
| API用户 | 可选 | API访问 | 受限数据 | ✅ **需人工定义API权限** |

### 5.2 权限控制策略
| 信息项 | 优先级 | 格式要求 | 示例 | 人工确认点 |
|--------|--------|----------|------|------------|
| 认证方式 | 必填 | 认证类型 | JWT, OAuth2, SAML | ✅ **需人工选择认证方式** |
| 权限粒度 | 必填 | 控制级别 | 功能级, 数据级, 字段级 | ✅ **需人工确定权限粒度** |
| 会话管理 | 必填 | 会话策略 | 超时时间, 并发限制 | ✅ **需人工设置会话参数** |
| 多租户隔离 | 可选 | 隔离策略 | 数据库级, 表级, 行级 | ✅ **需人工选择隔离策略** |

### 5.3 审计要求
| 信息项 | 优先级 | 格式要求 | 示例 | 人工确认点 |
|--------|--------|----------|------|------------|
| 审计日志范围 | 必填 | 操作类型 | 登录, 数据修改, 权限变更 | ✅ **需人工确定审计范围** |
| 日志保留期限 | 必填 | 时间长度 | 1年, 3年, 永久 | ✅ **需人工设置保留期限** |
| 敏感操作记录 | 必填 | 操作清单 | 密码修改, 权限授予 | ✅ **需人工定义敏感操作** |

---

## 🛡️ 六、安全基线信息收集

### 6.1 密钥管理
| 信息项 | 优先级 | 格式要求 | 示例 | 人工确认点 |
|--------|--------|----------|------|------------|
| 密钥管理服务 | 必填 | 服务名称 | HashiCorp Vault, AWS KMS | ✅ **需人工选择密钥管理方案** |
| 密钥轮换策略 | 必填 | 轮换周期 | 90天, 180天 | ✅ **需人工设置轮换周期** |
| 临时凭证有效期 | 必填 | 时间长度 | 1小时, 24小时 | ✅ **需人工设置凭证有效期** |
| 密钥强度要求 | 必填 | 加密标准 | AES-256, RSA-2048 | ✅ **需人工确认加密标准** |

**⚠️ 预防提示**:
- 绝对禁止硬编码任何密钥或密码
- 所有密钥必须通过安全的密钥管理服务获取
- 定期进行密钥安全审计

### 6.2 输入验证和输出编码
| 信息项 | 优先级 | 格式要求 | 示例 | 人工确认点 |
|--------|--------|----------|------|------------|
| 输入验证策略 | 必填 | 验证规则 | 白名单验证, 长度限制 | ✅ **需人工定义验证规则** |
| 危险函数黑名单 | 必填 | 函数清单 | eval, Function, exec | ✅ **需人工确认危险函数清单** |
| 输出编码方式 | 必填 | 编码类型 | HTML编码, URL编码 | ✅ **需人工选择编码方式** |
| 文件上传限制 | 必填 | 限制规则 | 类型白名单, 大小限制 | ✅ **需人工设置上传限制** |

### 6.3 安全扫描和测试
| 信息项 | 优先级 | 格式要求 | 示例 | 人工确认点 |
|--------|--------|----------|------|------------|
| 静态代码扫描 | 必填 | 扫描工具 | SonarQube, Checkmarx | ✅ **需人工配置代码扫描** |
| 依赖漏洞扫描 | 必填 | 扫描工具 | OWASP Dependency Check | ✅ **需人工配置依赖扫描** |
| 渗透测试计划 | 可选 | 测试频率 | 每季度, 每半年 | ✅ **需人工制定测试计划** |
| 安全基线检查 | 必填 | 检查清单 | OWASP Top 10 | ✅ **需人工确认安全基线** |

---

## ⚙️ 七、操作规范信息收集

### 7.1 部署和发布
| 信息项 | 优先级 | 格式要求 | 示例 | 人工确认点 |
|--------|--------|----------|------|------------|
| 部署策略 | 必填 | 策略类型 | 蓝绿部署, 滚动更新 | ✅ **需人工选择部署策略** |
| CI/CD工具 | 必填 | 工具名称 | Jenkins, GitLab CI | ✅ **需人工选择CI/CD工具** |
| 环境晋升流程 | 必填 | 流程步骤 | dev→test→staging→prod | ✅ **需人工定义晋升流程** |
| 回滚策略 | 必填 | 回滚方式 | 自动回滚, 手动回滚 | ✅ **需人工制定回滚策略** |

### 7.2 监控和告警
| 信息项 | 优先级 | 格式要求 | 示例 | 人工确认点 |
|--------|--------|----------|------|------------|
| 关键指标监控 | 必填 | 指标清单 | CPU, 内存, 响应时间 | ✅ **需人工定义监控指标** |
| 告警阈值设置 | 必填 | 阈值参数 | CPU>80%, 错误率>5% | ✅ **需人工设置告警阈值** |
| 告警通知方式 | 必填 | 通知渠道 | 邮件, 短信, Slack | ✅ **需人工配置通知方式** |
| 值班轮换制度 | 可选 | 轮换规则 | 7x24小时, 工作时间 | ✅ **需人工制定值班制度** |

### 7.3 备份和恢复
| 信息项 | 优先级 | 格式要求 | 示例 | 人工确认点 |
|--------|--------|----------|------|------------|
| 备份策略 | 必填 | 备份类型 | 全量备份, 增量备份 | ✅ **需人工制定备份策略** |
| 备份频率 | 必填 | 时间间隔 | 每日, 每周 | ✅ **需人工设置备份频率** |
| 备份存储位置 | 必填 | 存储路径 | `/backups/daily` | ✅ **需人工指定备份位置** |
| 恢复测试计划 | 必填 | 测试频率 | 每月, 每季度 | ✅ **需人工制定恢复测试计划** |
| RTO/RPO目标 | 必填 | 时间目标 | RTO<4h, RPO<1h | ✅ **需人工设置恢复目标** |

---

## 📚 八、文档知识库信息收集

### 8.1 技术文档要求
| 文档类型 | 优先级 | 格式要求 | 示例 | 人工确认点 |
|----------|--------|----------|------|------------|
| API文档 | 必填 | 文档标准 | OpenAPI 3.0, Swagger | ✅ **需人工确认API文档标准** |
| 架构文档 | 必填 | 文档格式 | ADR, C4模型 | ✅ **需人工选择架构文档格式** |
| 数据库设计文档 | 必填 | 文档内容 | ER图, 表结构说明 | ✅ **需人工提供数据库设计** |
| 部署文档 | 必填 | 文档内容 | 环境配置, 部署步骤 | ✅ **需人工编写部署文档** |

### 8.2 业务文档要求
| 文档类型 | 优先级 | 格式要求 | 示例 | 人工确认点 |
|----------|--------|----------|------|------------|
| 需求文档 | 必填 | 文档格式 | PRD, 用户故事 | ✅ **需人工提供需求文档** |
| 业务流程图 | 必填 | 图表格式 | BPMN, 流程图 | ✅ **需人工绘制业务流程** |
| 用户手册 | 可选 | 文档格式 | 操作指南, FAQ | ✅ **需人工编写用户手册** |
| 测试用例 | 必填 | 用例格式 | 功能测试, 接口测试 | ✅ **需人工编写测试用例** |

### 8.3 AI Agent专用信息
| 信息项 | 优先级 | 格式要求 | 示例 | 人工确认点 |
|--------|--------|----------|------|------------|
| 业务术语表 | 必填 | 中英文对照 | 能力模型=Capability Model | ✅ **需人工提供术语表** |
| 领域Prompt模板 | 必填 | 模板格式 | 代码生成, 文档生成 | ✅ **需人工创建Prompt模板** |
| 代码风格示例 | 必填 | 代码样例 | 命名规范, 注释风格 | ✅ **需人工提供代码示例** |
| 常见错误清单 | 必填 | 错误类型 | 配置错误, 安全漏洞 | ✅ **需人工总结常见错误** |

---

## ✅ 信息收集完成度检查

### 必填项完成度统计
- [ ] 开发工具栈: ___/12 项完成
- [ ] 技术堆栈规范: ___/8 项完成  
- [ ] 环境与依赖: ___/11 项完成
- [ ] 基础设施配置: ___/13 项完成
- [ ] 权限矩阵: ___/9 项完成
- [ ] 安全基线: ___/12 项完成
- [ ] 操作规范: ___/12 项完成
- [ ] 文档知识库: ___/12 项完成

**总完成度**: ___/89 项 (___%)

### 关键风险项检查
- [ ] 是否存在硬编码密钥？
- [ ] 是否配置了输入验证？
- [ ] 是否设置了备份策略？
- [ ] 是否定义了安全基线？
- [ ] 是否建立了监控告警？

---

## 🎯 使用建议

### 对AI Agent的建议
1. **严格按照清单收集信息** - 不要跳过任何必填项
2. **主动询问不明确的信息** - 遇到模糊描述时及时澄清
3. **验证信息的一致性** - 确保不同模块间的信息不冲突
4. **记录收集过程中的问题** - 为后续项目积累经验

### 对项目负责人的建议
1. **提前准备相关信息** - 在项目启动前准备好所有必要信息
2. **指定专人配合** - 安排技术负责人协助信息收集
3. **定期更新信息** - 项目过程中及时更新变更的信息
4. **建立信息模板** - 为类似项目建立标准化信息模板

---

---

## 🔄 九、开发工作流程指导

### 9.1 前端开发工作流程
| 信息项 | 优先级 | 格式要求 | 示例 | 人工确认点 |
|--------|--------|----------|------|------------|
| Vue.js组件开发标准 | 必填 | 标准组件模板 | `<script setup lang="ts">`, Props接口定义, emit事件声明 | - |
| TypeScript类型定义 | 必填 | 类型文件结构 | `types/api.ts`, `interface User {}`, 严格模式配置 | - |
| Element Plus使用指南 | 必填 | 按需引入配置 | `unplugin-vue-components`, 全局样式变量, 主题色配置 | - |
| 状态管理规范 | 必填 | Pinia Store模板 | `defineStore('user', () => {})`, 持久化插件配置 | - |
| API调用标准 | 必填 | Axios实例配置 | 请求/响应拦截器, 统一错误处理, 超时设置10s | - |
| 路由权限控制 | 必填 | 路由守卫模板 | `beforeEach`权限检查, 动态路由加载, 404页面 | ✅ **需人工确认权限角色定义** |
| 构建部署流程 | 必填 | Vite配置模板 | 环境变量配置, 代码分割, CDN资源配置 | ✅ **需人工确认部署环境** |

**⚠️ 预防提示**:
- 使用组合式API和TypeScript提高代码质量
- 遵循单一职责原则拆分组件
- 建立统一的错误处理和加载状态管理

### 9.2 后端开发工作流程
| 信息项 | 优先级 | 格式要求 | 示例 | 人工确认点 |
|--------|--------|----------|------|------------|
| 项目结构规范 | 必填 | 标准目录结构 | `controller/service/mapper/entity`, 按功能模块分包 | - |
| RESTful API设计 | 必填 | 标准API规范 | `GET /users`, `POST /users`, 统一响应格式`Result<T>` | - |
| 数据访问层标准 | 必填 | MyBatis-Plus模板 | `BaseMapper<T>`, `@TableName`, 分页插件配置 | - |
| 业务逻辑层规范 | 必填 | Service层模板 | `@Transactional`, 接口+实现类, DTO转换 | - |
| 控制器层标准 | 必填 | Controller模板 | `@RestController`, `@Valid`参数校验, Swagger注解 | - |
| 统一异常处理 | 必填 | 异常处理模板 | `@ControllerAdvice`, 错误码枚举, 统一日志格式 | - |
| 安全认证实现 | 必填 | JWT配置模板 | Spring Security配置, Token生成/验证, 权限注解 | ✅ **需人工确认权限角色设计** |

**⚠️ 预防提示**:
- 使用标准的三层架构和依赖注入
- 所有外部调用都要有超时和重试机制
- 敏感操作必须记录操作日志

### 9.3 数据库开发工作流程
| 信息项 | 优先级 | 格式要求 | 示例 | 人工确认点 |
|--------|--------|----------|------|------------|
| 表设计规范 | 必填 | 标准命名规则 | `user_info`, `created_at`, `is_deleted`, 统一字段类型 | - |
| 版本控制策略 | 必填 | SQL脚本管理 | `V1.0.1__Create_user_table.sql`, 增量更新脚本 | - |
| 索引设计指导 | 必填 | 索引创建规则 | 主键自增, 查询字段加索引, 复合索引左前缀原则 | - |
| 数据一致性机制 | 必填 | 事务管理模板 | `@Transactional`, 乐观锁`version`字段, 软删除 | - |
| 备份恢复流程 | 必填 | 备份脚本模板 | 每日自动备份, 保留30天, 恢复测试脚本 | ✅ **需人工确认备份存储位置** |

### 9.4 API开发和集成工作流程
| 信息项 | 优先级 | 格式要求 | 示例 | 人工确认点 |
|--------|--------|----------|------|------------|
| 接口设计标准 | 必填 | 标准API格式 | Swagger注解, 统一响应体, HTTP状态码规范 | - |
| 前后端联调流程 | 必填 | 标准协作流程 | 1.接口文档 2.Mock数据 3.联调测试 4.集成测试 | - |
| API版本管理 | 必填 | 版本控制规则 | `/api/v1/users`, 向下兼容3个版本, 废弃通知期6个月 | - |
| 接口测试方法 | 必填 | 测试用例模板 | 正常/异常/边界值测试, Postman集合, 自动化测试 | - |

---

## 🎯 十、功能性需求开发指导

### 10.1 需求分析和设计流程
| 信息项 | 优先级 | 格式要求 | 示例 | 人工确认点 |
|--------|--------|----------|------|------------|
| 需求转换方法 | 必填 | 标准分析模板 | 用户故事→验收标准→技术任务→开发计划 | - |
| 模块划分原则 | 必填 | 模块化设计规则 | 按业务领域分模块, 单一职责, 接口隔离 | - |
| UI设计规范 | 必填 | 界面设计标准 | 响应式布局, 8px栅格系统, 无障碍设计 | - |
| 数据模型设计 | 必填 | 数据建模模板 | 实体关系图, 字段规范, 索引设计 | ✅ **需人工确认业务实体关系** |

**⚠️ 预防提示**:
- 使用用户故事格式描述需求: "作为...我希望...以便..."
- 每个功能都要有明确的验收标准
- 优先考虑核心功能，避免功能蔓延

### 10.2 开发实施标准
| 信息项 | 优先级 | 格式要求 | 示例 | 人工确认点 |
|--------|--------|----------|------|------------|
| 优先级排序原则 | 必填 | 标准排序方法 | P0核心功能→P1重要功能→P2优化功能→P3未来功能 | - |
| 模块化开发指导 | 必填 | 开发规范模板 | 组件库复用, 工具函数抽取, 配置文件管理 | - |
| 测试要求 | 必填 | 测试标准模板 | 单元测试>70%, 集成测试覆盖主流程, E2E测试 | - |
| 代码审查流程 | 必填 | 审查检查清单 | 功能正确性, 代码规范, 性能影响, 安全检查 | - |

### 10.3 交付和验收标准
| 信息项 | 优先级 | 格式要求 | 示例 | 人工确认点 |
|--------|--------|----------|------|------------|
| 完成度评估标准 | 必填 | 评估检查清单 | 功能实现✓, 测试通过✓, 文档完整✓, 性能达标✓ | - |
| 性能质量要求 | 必填 | 标准性能指标 | 页面加载<3s, API响应<1s, 错误率<0.1% | ✅ **需人工确认具体性能要求** |
| 用户验收流程 | 必填 | 验收流程模板 | 1.功能演示 2.用户测试 3.问题收集 4.修复验证 | - |
| 文档交付要求 | 必填 | 文档清单模板 | README.md, API文档, 部署文档, 用户手册 | - |

---

## ✅ 更新后的信息收集完成度检查

### 必填项完成度统计
- [ ] 开发工具栈: ___/12 项完成
- [ ] 技术堆栈规范: ___/8 项完成
- [ ] 环境与依赖: ___/11 项完成
- [ ] 基础设施配置: ___/21 项完成 (新增开发环境凭证8项)
- [ ] 权限矩阵: ___/9 项完成
- [ ] 安全基线: ___/12 项完成
- [ ] 操作规范: ___/12 项完成
- [ ] 文档知识库: ___/12 项完成
- [ ] **开发工作流程指导**: ___/19 项完成 (其中16项AI可自主完成)
- [ ] **功能性需求开发指导**: ___/11 项完成 (其中9项AI可自主完成)

**总完成度**: ___/127 项 (___%)
**AI自主完成度**: 94/127 项 (74.0%) - 适合开发新手使用

### 关键风险项检查
- [ ] 是否存在硬编码密钥？
- [ ] 是否配置了输入验证？
- [ ] 是否设置了备份策略？
- [ ] 是否定义了安全基线？
- [ ] 是否建立了监控告警？
- [ ] **是否收集了开发环境凭证？** (AI Agent工作必需的认证信息)
- [ ] **是否建立了凭证安全管理机制？** (防止凭证泄露和滥用)
- [ ] **是否制定了开发工作流程？** (AI可基于最佳实践自动生成)
- [ ] **是否明确了功能开发标准？** (AI可提供标准模板和检查清单)

---

**📌 重要提醒**: 本清单的质量直接影响项目开发的成功率。请确保所有标记为"需人工确认"的项目都得到明确的答复，避免因信息不完整导致的开发问题。

**🔄 版本控制**: 建议将完成的信息清单纳入项目版本控制，作为项目的重要基础文档进行维护。
